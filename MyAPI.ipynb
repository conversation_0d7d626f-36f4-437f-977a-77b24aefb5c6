#%%
import json
from datetime import datetime, timedelta

import pandas as pd
import requests

## ----
import tushare as ts

pro = ts.pro_api('f60827dcc933679a892b5bc8cd1bf3c8ebd541f3b7468208b925bc65')
#%%
code="600704"

df = pro.daily(ts_code=f"{code}.SH", start_date='20241202', end_date='20250606')
df
# 保存为csv文件，index=False表示不保存行索引
df.to_csv(f'/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_daily_data.csv', index=False)

# 个股资金流
import akshare as ak
stock_individual_fund_flow_df = ak.stock_individual_fund_flow(stock=code, market="sh")
stock_individual_fund_flow_df
stock_individual_fund_flow_df.to_csv(f'/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_individual_fund_flow.csv', index=False)
#%%
# 融资融劵
url = f"https://datacenter-web.eastmoney.com/api/data/v1/get?reportName=RPTA_WEB_RZRQ_GGMX&columns=ALL&source=WEB&sortColumns=date&sortTypes=-1&pageNumber=1&pageSize=120&filter=(scode%3D%22601008%22)&pageNo=1&_=1745987097967"
response = requests.get(url)
response.raise_for_status()
# cleaned_json = response.text.strip().lstrip('datatable3109753(').rstrip(');')
# print(response.text)
# 解析JSON字符串为Python字典
parsed_data = json.loads(response.text)
# print(parsed_data)
# 提取data字段（result -> data）
data_list = parsed_data['result']['data']
df = pd.DataFrame(data_list)
end_date = datetime.now()
start_date = end_date + timedelta(days=-10)
print(start_date, end_date)
df["DATE"] = pd.to_datetime(df["DATE"])
df = df[(df["DATE"] >= start_date) & (df["DATE"] <= end_date)]
df

#%%
# ----
#%%
# 深圳市场个股资金异动数据，可获取近半年数据
url = f"https://push2ex.eastmoney.com/getStockChanges?ut=7eea3edcaed734bea9cbfc24409ed989&date=20250508&dpt=wzchanges&code=002251&market=0&_=1712143839125"
response = requests.get(url)
response.raise_for_status()
print(response.text)
json_data_str = response.text.split('(', 1)[1].rsplit(')', 1)[0]
data = json.loads(json_data_str)
print(data)
df = None
if data["data"] is not None:
    result = data["data"]["data"]
    df = pd.DataFrame(result)
df
#%%
for _, row in df.iterrows():
    print(row["tm"])
#%%
# -------
#%%
# 上海市场个股资金异动数据，可获取近3个月数据｜
url = f"https://push2ex.eastmoney.com/getStockChanges?cb=jQuery351018695419794568324_1712144160863&ut=7eea3edcaed734bea9cbfc24409ed989&date=20250424&dpt=wzchanges&code=600391&market=1&_=1712144160868"
response = requests.get(url)
response.raise_for_status()
json_data_str = response.text.split('(', 1)[1].rsplit(')', 1)[0]
data = json.loads(json_data_str)
if "data" in data and "data" in data["data"]:
    result = data["data"]["data"]
pd.DataFrame(result)
#%%
# -------
#%%
url = f"https://push2his.eastmoney.com/api/qt/stock/kline/get?cb=jQuery35103075140001622887_1746077211921&fields1=f1%2Cf2%2Cf3%2Cf4%2Cf5&fields2=f51%2Cf53%2Cf59&secid=0.000001&ut=fa5fd1943c7b386f172d6893dbfba10b&lmt=10&klt=101&fqt=1&end=20500000&_=1746077211932"
response = requests.get(url)
response.raise_for_status()
json_data_str = response.text.split('(', 1)[1].rsplit(')', 1)[0]
data = json.loads(json_data_str)
klines = data["data"]["klines"]
df = pd.DataFrame([k.split(",") for k in klines],
                  columns=["日期", "收盘价", "涨跌幅"])

# 类型转换与格式化
df = df.astype({
    "日期": "datetime64[ns]",
    "收盘价": float,
    "涨跌幅": float
})
df
#%%
# 美股指数
url = "https://push2.eastmoney.com/api/qt/clist/get?np=1&fltt=1&invt=2&cb=jQuery37107939765842495493_1746197391789&fs=i%3A100.NDX%2Ci%3A100.DJIA%2Ci%3A100.SPX&fields=f12%2Cf13%2Cf14%2Cf292%2Cf1%2Cf2%2Cf4%2Cf3%2Cf152%2Cf17%2Cf28%2Cf15%2Cf16%2Cf18%2Cf7%2Cf124&fid=f3&pn=1&pz=20&po=1&dect=1&ut=fa5fd1943c7b386f172d6893dbfba10b&wbp2u=9651057084417778%7C0%7C1%7C0%7Cweb&_=1746197391793"
response = requests.get(url)
response.raise_for_status()
response_data = json.loads(response.text.split('(', 1)[1].rsplit(')', 1)[0])
print(response_data)

import pandas as pd

# 提取 data['data']['diff'] 部分的内容，包含所有条目
diff_data = response_data['data']['diff']

# 创建 DataFrame，从上图提取字段
df = pd.DataFrame([
    {
        "代码": f"{item['f12']}",
        "名称": item['f14'],
        "最新价": item['f2'] / 100,
        "涨跌额": item['f4'] / 100,
        "涨跌幅": f"{item['f3'] / 100:.2f}%",
        "开盘价": item['f17'] / 100,
        "最高价": item['f15'] / 100,
        "最低价": item['f16'] / 100,
        "昨收价": item['f18'] / 100,
        "振幅": f"{item['f7'] / 100:.2f}%"
    }
    for index, item in enumerate(diff_data)
])

df

#%%
## ----
import tushare as ts

pro = ts.pro_api('f60827dcc933679a892b5bc8cd1bf3c8ebd541f3b7468208b925bc65')
#%%
# 初始化pro接口
pro = ts.pro_api('f60827dcc933679a892b5bc8cd1bf3c8ebd541f3b7468208b925bc65')

# 拉取数据
df = pro.stk_mins(**{
    "ts_code": "002049.SH",
    "freq": "60min",
    "start_date": "2024-12-02 09:00:00",
    "end_date": "2025-06-03 15:00:00",
    "limit": "",
    "offset": ""
}, fields=[
    "ts_code",
    "trade_time",
    "close",
    "open",
    "high",
    "low",
    "vol",
    "amount"
])
df
#%%
code="605358"
df = pro.daily(ts_code=f"{code}.SH", start_date='20241202', end_date='20250604')
df
# 保存为csv文件，index=False表示不保存行索引
df.to_csv(f'/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_daily_data.csv', index=False)
#%%

#%%
# 获取实时行情
# 最新价（f43）
# 涨跌幅（f170）
# 成交量（f47）
# 成交额（f48）
# 最高价（f44）
# 最低价（f45）
# 开盘价（f46）
# 昨收价（f60）
symbol = "000001"
market_code = 1 if symbol.startswith("6") else 0
url = "https://push2.eastmoney.com/api/qt/stock/get"
params = {
    "invt": 2,
    "fltt": 1,
    "fields": "f43,f170,f48,f47,f44,f45,f46,f60",
    "ut": "fa5fd1943c7b386f172d6893dbfba10b",
    "secid": f"{market_code}.{symbol}",
    "wbp2u": "|0|0|0|web",
    "dect": "1",
}
response = requests.get(url, params=params)
response.raise_for_status()
print(response.text)
#%%
