#%%
import pandas as pd
code = "600704"
float_shares = 5.1e9                   # 流通股本（股）——自行替换
daily_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_daily_data.csv"
fund_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_individual_fund_flow.csv"
daily_data = pd.read_csv(daily_path)
# 读取资金流数据
funds_data = pd.read_csv(fund_path)
#%%
# -*- coding: utf-8 -*-
"""
量能-换手堆积三信号 + K 线可视化
--------------------------------------------------------------
* TurnSpike： Turnover > MA20 + 1σ 且连续 2 天递增
* 堆量吸筹  ：TurnSpike & |pct_chg| < 1% & 主力净流入 > 0
* 堆量突破  ：TurnSpike & pct_chg > 3%  & 实体长阳(>50%)
* 堆量派发  ：TurnSpike & (长上影 OR 主力净流出 < 0)
--------------------------------------------------------------
"""

import pandas as pd
import numpy as np
from pathlib import Path
from pyecharts.charts import Kline, Scatter, Grid
from pyecharts import options as opts

# ------------------------------------------------------------
# 0. 参数区（改路径 / 流通股本即可）
# ------------------------------------------------------------
# daily_path = Path("daily_kline.csv")   # 含 trade_date, open, high, low, close, pct_chg, vol
# fund_path  = Path("moneyflow.csv")     # 含 日期, 主力净流入-净额
float_shares = 2.75e9                   # 流通股本（股）——自行替换
# 阈值
TURN_Z   = 1.0                         # 换手放大： > MA20 + 1σ
PCT_BREAK = 3.0                        # 长阳定义（%）
LONG_BODY = 0.5                        # 实体 > (high-low)*50%
# ------------------------------------------------------------

# 1. 读数据
daily = (pd.read_csv(daily_path, dtype={'trade_date': str})
           .rename(columns={'trade_date': 'date'})
           .assign(date=lambda d: pd.to_datetime(d['date']))
           .set_index('date').sort_index())

fund  = (pd.read_csv(fund_path, dtype={'日期': str})
           .rename(columns={'日期': 'date', '主力净流入-净额': 'mf_amt'})
           .assign(date=lambda f: pd.to_datetime(f['date']))
           .set_index('date').sort_index())

df = (daily.join(fund[['mf_amt']], how='left')
            .fillna({'mf_amt': 0}))       # 若缺资金数据设 0

# 2. 计算换手率 & TurnSpike
df['turn'] = df['vol']  / float_shares
df['turn_ma20']  = df['turn'].rolling(20).mean()
df['turn_std20'] = df['turn'].rolling(20).std()

cond_big = df['turn'] > (df['turn_ma20'] + TURN_Z * df['turn_std20'])
cond_up  = (df['turn'] > df['turn'].shift(1)) & (df['turn'].shift(1) > df['turn'].shift(2))
df['TurnSpike'] = cond_big & cond_up

# 3. 价格辅助形态
df['body']      = (df['close'] - df['open']).abs()
df['range']     = df['high']  - df['low']
df['upper_wick'] = df['high'] - df[['close','open']].max(axis=1)
# 小实体
small_body = df['body'] / df['range'] < 0.3

# 4. 三类信号
## 4.1 堆量吸筹
df['吸筹'] = df['TurnSpike'] & (df['pct_chg'].abs() <= 1.2) & (df['mf_amt'] > 0) & small_body

## 4.2 堆量突破
long_green = (df['pct_chg'] > PCT_BREAK) & \
             (df['body'] / df['range'] > LONG_BODY) & \
             (df['close'] > df['open'])          # 长阳实体
df['突破'] = df['TurnSpike'] & long_green

## 4.3 堆量派发
long_upper = df['upper_wick'] / df['range'] > 0.4
df['派发'] = df['TurnSpike'] & ((df['mf_amt'] < 0) | long_upper)

# 5. 可视化标记
## 5.1 K 线
dates = df.index.strftime('%Y-%m-%d').tolist()
k_vals = df[['open','close','low','high']].round(2).values.tolist()

kline = (Kline(init_opts=opts.InitOpts( height="480px"))
         .add_xaxis(dates)
         .add_yaxis("K线", k_vals,
                    itemstyle_opts=opts.ItemStyleOpts(color="#ec0000",
                                                      color0="#00da3c"))
         .set_global_opts(
              xaxis_opts=opts.AxisOpts(type_="category", boundary_gap=False,
                                       axislabel_opts=opts.LabelOpts(rotate=-45)),
              datazoom_opts=[opts.DataZoomOpts(type_="inside"), opts.DataZoomOpts()],
              tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),
              title_opts=opts.TitleOpts(title="量能-换手堆积三信号")
          )
)

## 5.2 信号散点
def scatter_series(mask, symbol, color, name):
    idx = df.index[mask]
    if idx.empty:
        return None
    return (Scatter()
            .add_xaxis(idx.strftime('%Y-%m-%d').tolist())
            .add_yaxis(name,
                       y_axis=df.loc[idx, 'high'].tolist(),   # 把点放 K 线顶端
                       symbol=symbol, symbol_size=12,
                       itemstyle_opts=opts.ItemStyleOpts(color=color),
                       label_opts=opts.LabelOpts(is_show=False)))

for m, sym, col, n in [(df['吸筹'], "triangle", "#2ca02c", "堆量吸筹"),
                       (df['突破'], "pin",      "#1f77b4", "堆量突破"),
                       (df['派发'], "diamond",  "#d62728", "堆量派发")]:
    sc = scatter_series(m, sym, col, n)
    if sc: kline.overlap(sc)

# 6. 渲染
html_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_k_turnover_spike_signals.html"
Grid(init_opts=opts.InitOpts(width="100%", height="450px")).add(kline, grid_opts=opts.GridOpts( pos_left="5%", pos_right="2%",
                                          pos_top="8%", height="85%")
          ).render(html_path)

print("✅ 已输出  turn_spike_signals.html   —— 浏览器打开即可查看三色信号点")

#%% md
#### 颜色 / 图形	条件概要	场景含义
* 绿三角	TurnSpike + 收盘 ±1% 内 + 主力净流入>0 + 小实体	堆量吸筹 —— 主力暗吸，洗浮筹
* 蓝五角星	TurnSpike + 实体长阳 (pct_chg>3%, 实体占比>50%)	堆量突破 —— 主力/游资点火、放量过前高
* 红菱形	TurnSpike + (长上影 or 主力净流出)	堆量派发 —— 高位换手＋派发预警
#%%
