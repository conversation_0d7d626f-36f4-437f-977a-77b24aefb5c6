#%% md
## 一、主要覆盖的底部类型
### 1. “急跌之后的快速抄底”（V 形反转型）
* 典型特征

    价格在短期（几日内）出现明显跳水或“触底杀跌”——收盘价跌破近 N 日低位。
    
    紧接着某一交易日，出现较大规模的“机构净买入”（大单+超大单持续正流入、Z-score 较高），并且当日价格出现小幅企稳或反弹。

* 算法捕捉逻辑

    价格 rank 接近 0（price_rank < BOT_Q），说明股价位于 N 日区间底部附近。
    
    过去 N 日内机构累计净买（flow_z > 閾值）明显偏正，说明主力开始在底部低吸。
    → 立刻把这一天标记为“V 形反转底”。

* 举例
    
    某票在 5 天之内从 10 元跌到 7.5 元，一度出现长下影线；次日主力大单净买并护盘，价格收出小阳。
    
    按算法，5 天跌幅把 price_rank 压到很低，而次日 flow_z 明显 > 0，满足两个条件 → 识别为底。

### 2. “下跌尾声的缓慢吸筹底”（U 形缓和底）
* 典型特征

    价格并非“直线断崖式暴跌”，而是连续多日或数周缓慢下行或横盘整理。
    
    这种下跌往往伴随成交量逐步萎缩，散户情绪低迷，主力悄无声息地进行小规模买入。
    
    当下跌足够让 price_rank 均匀进入低区时，即使当日价格没有大幅反弹，只要有明显的累计流入（flow_z > 0），也可视为“缓慢筑底”。

* 算法捕捉逻辑

    在长周期内（N 日）股价频繁在区间底部附近徘徊，price_rank 一直维持在低于阈值（比如 0.2）。
    
    而此时机构净买（算上过去 N 日之和并标准化）持续偏正，即 flow_z > +0.3 ——哪怕单日涨跌幅不大。
    → 识别出一个“潜伏式底部”，对应缓和型 U 底的阶段性底尖。

* 举例
    
    某票先后数周从 9.1→8.8→8.7→8.6，成交量不断萎缩；主力在区间 8.6 累计悄悄低吸，flow_z 显示净买累积。
    
    当 price_rank 持续 < 0.2 且 flow_z 突破阈值时，算法就会把当前日标为“底部”，即使 K 线并非明显大阳。

### 3. “横盘区间的支撑拐点”（区间底部轮动型）
* 典型特征

    某只股票在一个横盘区间反复震荡，比如长期在 6.0–6.5 之间窄幅波动。
    
    在每次区间下沿（6.0 左右）回踩时，若出现机构稍微加大买入力度，便意味着“本轮支撑有效，区间底部再次得到确认”。
    
    这时 price_rank 接近 0（因为近 N 日并无更低低点），flow_z 虽然不一定特别高，但只要高于票均水平，Z-score 也可突破阈值。

* 算法捕捉逻辑

    在区间下沿反复触底，过去 N 日的 roll_min 接近区间下限，导致 price_rank 一直偏低。
    
    当某一个回调触底日，flow_ratio（即近 N 日机构净买 / 成交总量）超过近期平均水平，flow_z 大于阈值。
    → 该日也会被识别为“区间底部”，提示资金在这个支撑位展开吸筹。

* 举例

    8.0–8.5 区间震荡，每次触及 8.0 附近就有大单买入。
    
    price_rank 始终在 0.0x−0.1x 之间，某日 flow_z 突破 0.3，就会提示“区间底部成立”，可短线参与区间震荡。
#%%
import pandas as pd
code = "600704"
daily_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_daily_data.csv"
fund_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_stock_individual_fund_flow.csv"
daily_data = pd.read_csv(daily_path)
# 读取资金流数据
funds_data = pd.read_csv(fund_path)
#%%
import pandas as pd
import numpy as np
from pyecharts.charts import Kline, Scatter
from pyecharts import options as opts

# === CONFIG ===
L = 20                 # 价格 & 资金滚动窗口
BOT_Q = 0.2            # 底部价格区位阈值
FLOW_Z_TH = 0.3        # 资金偏离阈值 (σ)
EPS = 1e-6

# === 1. 读取数据 ===
# daily_path = '9950f00b-2b1d-4988-b4d2-293ffb946b72.csv'
# fund_path  = 'b205a957-bd45-432c-9503-5c8f424f0da3.csv'

daily_raw = pd.read_csv(daily_path)
fund_raw  = pd.read_csv(fund_path)

# 重命名为英文列（假设 daily_raw 带有 OHLC 字段）
daily = daily_raw.rename(columns={
    'trade_date': 'trade_date',
    'open': 'open',
    'high': 'high',
    'low': 'low',
    'close': 'close'
})
funds = fund_raw.rename(columns={
    '日期': 'trade_date',
    '小单净流入-净额': 'small_net_amt',
    '大单净流入-净额': 'large_net_amt',
    '超大单净流入-净额': 'elg_net_amt'
})

daily['trade_date'] = pd.to_datetime(daily['trade_date'], format='%Y%m%d')
funds['trade_date'] = pd.to_datetime(funds['trade_date'], format='%Y-%m-%d')

# 只保留需要列
daily = daily[['trade_date', 'open', 'high', 'low', 'close']]
funds = funds[['trade_date', 'small_net_amt', 'large_net_amt', 'elg_net_amt']]

# 合并数据
df = pd.merge(
    daily,
    funds,
    on='trade_date',
    how='inner'
).set_index('trade_date').sort_index()

# === 2. 指标计算 ===
# 2.1 价格区位 price_rank，带振幅保护
df['roll_max'] = df['close'].rolling(L).max()
df['roll_min'] = df['close'].rolling(L).min()
rolling_mean_close = df['close'].rolling(L).mean()
rng = df['roll_max'] - df['roll_min']
valid_range = rng > rolling_mean_close * 0.02  # 过去 20 日振幅至少占均价 2%
df['price_rank'] = np.where(
    valid_range,
    (df['close'] - df['roll_min']) / (rng + EPS),
    np.nan
)

# 2.2 资金倾向 flow_balance → flow_z
df['large_net_amt_comb'] = df['large_net_amt'] + df['elg_net_amt']
df['flow_balance'] = (df['large_net_amt_comb'] - df['small_net_amt']).rolling(L).sum()

# 估算成交活跃度：使用绝对净额之和做归一化
df['gross_flow'] = (
    df['large_net_amt_comb'].abs() + df['small_net_amt'].abs()
).rolling(L).sum()
df['flow_ratio'] = df['flow_balance'] / (df['gross_flow'] + EPS)

# 资金 Z-score
mu = df['flow_ratio'].rolling(L).mean()
std = df['flow_ratio'].rolling(L).std() + EPS
df['flow_z'] = (df['flow_ratio'] - mu) / std

# === 3. 生成 K 线数据和底部信号 ===
# K 线日期列表
kline_dates = df.index.strftime('%Y-%m-%d').tolist()
# K 线 OHLC 数值列表 [open, close, low, high]
kline_values = df[['open', 'close', 'low', 'high']].round(2).values.tolist()

# 底部信号标记
df['bottom_signal'] = 0
df.loc[
    (df['price_rank'] < BOT_Q) &
    (df['flow_z'] > FLOW_Z_TH),
    'bottom_signal'
] = 1
bottom_df = df[df['bottom_signal'] == 1]

# 底部信号的日期和价格位置（用 low*0.98 放在蜡烛下方）
bottom_dates = bottom_df.index.strftime('%Y-%m-%d').tolist()
bottom_prices = (bottom_df['low'] * 0.98).round(2).tolist()

# === 4. 绘制 pyecharts K 线图并叠加底部散点 ===
kline = (
    Kline(init_opts=opts.InitOpts(width="100%", height="400px"))
    .add_xaxis(kline_dates)
    .add_yaxis("K Line", kline_values)
    .set_global_opts(
        title_opts=opts.TitleOpts(title=f"{code} K 线图与底部信号"),
        xaxis_opts=opts.AxisOpts(type_="category"),
        yaxis_opts=opts.AxisOpts(is_scale=True),
        datazoom_opts=[opts.DataZoomOpts(type_="slider", is_show=True)],
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross")
    )
)

scatter = (
    Scatter()
    .add_xaxis(bottom_dates)
    .add_yaxis(
        "Bottom Signal",
        bottom_prices,
        symbol="circle",
        symbol_size=10,
        itemstyle_opts=opts.ItemStyleOpts(color="green")
    )
)

kline.overlap(scatter)

# 渲染并保存为 HTML
html_path = f"/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/{code}_kline_with_bottom_signals.html"
kline.render(html_path)

#%%

#%%

#%%
