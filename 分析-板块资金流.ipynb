#%%
csv_path = "/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/blank_fund.csv"  # 改成实际文件名

#%%
import re
import pandas as pd
from pathlib import Path
from pyecharts.charts import Line, Grid
from pyecharts import options as opts

# ---------------- 1. 路径 & 读数 ---------------- #

# 1) 读取
df = pd.read_csv(csv_path)
# -*- coding: utf-8 -*-
"""
行业资金面 3 合 1 面板（近 25 日）
---------------------------------------------------------------
1) 热力图：行业×日期 主力净流入 (亿元)
2) 左柱  ：25 日均净占比 流入 TOP-10   — 绿色
3) 右柱  ：25 日均净占比 流出 TOP-10   — 红色
4) 折线  ：流入榜首行业 25 日净占比趋势
---------------------------------------------------------------
列名要求（大小写不限，自动匹配）：
  · *_net_inflow   — 金额字段（正=流入 负=流出）
  ·  large / super / elg / major   → 视作主力档位
  · major_rate      — 主力净占比 (%)
---------------------------------------------------------------
依赖：pandas  +  pyecharts
pip install pandas pyecharts -U
"""       # 改成你的文件
df["date"] = pd.to_datetime(df["date"])

# ====== 2. 自动识别列 ======
# 金额列：以 _net_inflow 结尾
amt_cols = [c for c in df.columns if re.search(r"_net_inflow$", c, flags=re.I)]
if not amt_cols:
    raise ValueError("CSV 里没有 *_net_inflow 列")

# 主力列：含 large / super / major / elg
main_cols = [c for c in amt_cols if re.search(r"(large|super|major|elg)", c, flags=re.I)]
if not main_cols:               # 若无细分列，就用全部当主力
    main_cols = amt_cols

df["main_net"]  = df[main_cols].sum(axis=1)
df["total_net"] = df[amt_cols].sum(axis=1)

# 主力净占比列
rate_col = next((c for c in df.columns if re.fullmatch(r"major_rate", c, flags=re.I)), None)
if rate_col is None:
    raise ValueError("找不到 major_rate 列")

# ====== 3. 取最近 25 天窗口 ======
latest = df["date"].max()
win = df[df["date"].between(latest - pd.Timedelta(days=24), latest)]

# ====== 4. 热力图数据 (亿元) ======
pivot = (
    win.pivot_table(index="date", columns="name", values="main_net", aggfunc="sum")
    / 1e8
)
dates   = pivot.index.strftime("%m-%d").tolist()
sectors = pivot.columns.tolist()
heat_val = [[j, i, round(pivot.iloc[i, j], 2)]
            for i in range(len(dates)) for j in range(len(sectors))]

heat = (
    HeatMap()
    .add_xaxis(sectors)
    .add_yaxis("", dates, heat_val, label_opts=opts.LabelOpts(is_show=False))
    .set_global_opts(
        title_opts=opts.TitleOpts(title="近 25 日行业主力净流入 (亿元)", pos_top="1%"),
        visualmap_opts=opts.VisualMapOpts(
            min_=-pivot.abs().max().max(),
            max_= pivot.abs().max().max(),
            range_color=["#d73027", "#ffffff", "#2ca25f"]
        ),
        xaxis_opts=opts.AxisOpts(type_="category", axislabel_opts=opts.LabelOpts(rotate=-45)),
        yaxis_opts=opts.AxisOpts(type_="category")
    )
)

# ====== 5. 25 日均净占比：流入 / 流出 TOP-10 ======
avg_rate = win.groupby("name")[rate_col].mean()

in_top  = avg_rate[avg_rate > 0].nlargest(10).sort_values()
out_top = avg_rate[avg_rate < 0].nsmallest(10).sort_values(ascending=False)

bar_in = (
    Bar()
    .add_xaxis(in_top.index.tolist())
    .add_yaxis("25 日均净占比", [round(v, 2) for v in in_top.tolist()],
               itemstyle_opts=opts.ItemStyleOpts(color="#2ca25f"),
               label_opts=opts.LabelOpts(is_show=True, position="right"))
    .reversal_axis()
    .set_global_opts(
        title_opts=opts.TitleOpts(title="流入 TOP-10", pos_top="38%", pos_left="3%"),
        xaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(formatter="{value}%")),
        yaxis_opts=opts.AxisOpts(name="行业"),
        legend_opts=opts.LegendOpts(is_show=False),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="shadow")
    )
)

bar_out = (
    Bar()
    .add_xaxis(out_top.index.tolist())
    .add_yaxis("25 日均净占比", [round(v, 2) for v in out_top.tolist()],
               itemstyle_opts=opts.ItemStyleOpts(color="#d73027"),
               label_opts=opts.LabelOpts(is_show=True, position="right"))
    .reversal_axis()
    .set_global_opts(
        title_opts=opts.TitleOpts(title="流出 TOP-10", pos_top="38%", pos_left="53%"),
        xaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(formatter="{value}%")),
        yaxis_opts=opts.AxisOpts(name="行业"),
        legend_opts=opts.LegendOpts(is_show=False),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="shadow")
    )
)

# ====== 6. 折线：流入榜首行业 25 日净占比 ======
top_sector = in_top.idxmax() if not in_top.empty else avg_rate.idxmax()
sector_df  = win[win["name"] == top_sector].sort_values("date")
line = (
    Line()
    .add_xaxis(sector_df["date"].dt.strftime("%m-%d").tolist())
    .add_yaxis(top_sector, [round(v, 2) for v in sector_df[rate_col].tolist()],
               is_smooth=True, is_symbol_show=False,
               linestyle_opts=opts.LineStyleOpts(width=2))
    .set_global_opts(
        title_opts=opts.TitleOpts(title=f"{top_sector} 25 日净占比趋势", pos_top="71%"),
        xaxis_opts=opts.AxisOpts(type_="category", boundary_gap=False,
                                 axislabel_opts=opts.LabelOpts(rotate=-45)),
        yaxis_opts=opts.AxisOpts(name="%"),
        legend_opts=opts.LegendOpts(is_show=False),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross")
    )
)

# ====== 7. 组合 Grid ======
grid = Grid(init_opts=opts.InitOpts(width="100%", height="920px"))\
    .add(heat,    grid_opts=opts.GridOpts(pos_left="5%",  pos_right="5%", pos_top="5%",  height="30%"))\
    .add(bar_in,  grid_opts=opts.GridOpts(pos_left="3%",  pos_right="53%",pos_top="38%", height="24%"))\
    .add(bar_out, grid_opts=opts.GridOpts(pos_left="53%", pos_right="3%", pos_top="38%", height="24%"))\
    .add(line,    grid_opts=opts.GridOpts(pos_left="5%",  pos_right="5%", pos_top="70%", height="25%"))
print("✅ 已生成 sector_fund_panel25.html —— 打开即可查看三合一面板")


# ---- 7. 拼到同一页 ----

grid.render("/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/blank_flow_line.html")
print("✅ 已生成  blank_flow_line.html  —— 浏览器打开即可交互查看")
#%%
