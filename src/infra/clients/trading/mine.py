import json
from datetime import date
from typing import Optional

import pandas as pd
import requests
from loguru import logger

from src.infra.clients.trading.schema import DateTimeFormat, AStockMarket
from src.infra.clients.trading.utils import get_zh_stock_market, transform_nan_to_none
from src.infra.utils import retry_network


class MyClient:

    @retry_network
    def get_margin_trading_n_securities(
        self, code: str, size: int = 60, asc: bool = False
    ) -> pd.DataFrame:
        """
        融资融劵
        :param code:
        :return: DATE, RZYE(融资余额), RZMRE(融资买入额), RZCHE(融资偿还额), RQYL(融劵余量)，RQYE(融劵余额), RQMCL(融劵卖出量)，RQCHL(融资偿还量)
        """
        sorted_type = 0 if asc else -1

        url = f"https://datacenter-web.eastmoney.com/api/data/v1/get?callback=datatable3109753&reportName=RPTA_WEB_RZRQ_GGMX&columns=ALL&source=WEB&sortColumns=date&sortTypes={sorted_type}&pageNumber=1&pageSize={size}&filter=(scode%3D%22{code}%22)&pageNo=1&_=1745987097967"
        response = requests.get(url)
        response.raise_for_status()
        cleaned_json = response.text.strip().lstrip("datatable3109753(").rstrip(");")
        parsed_data = json.loads(cleaned_json)
        if parsed_data["result"]:
            data_list = parsed_data["result"].get("data", [])
            df = pd.DataFrame(data_list)
            df["DATE"] = pd.to_datetime(df["DATE"]).dt.date
            return df

        df = pd.DataFrame()
        return df

    @retry_network
    def get_stock_uma(self, code: str, date: date) -> pd.DataFrame:
        """
        沪深市场个股资金异动数据，可获取近3个月数据， 这个接口最好每 5 秒请求一次
        :param code:
        :return: t(异动类型), v(异动量)
        """
        market_str = get_zh_stock_market(code)
        if AStockMarket.SZ.value == market_str:
            market_tag = "0"
        elif AStockMarket.SH.value == market_str:
            market_tag = "1"
        else:
            return pd.DataFrame()

        date_str = date.strftime(DateTimeFormat.DATE_FORMAT_1.value)
        url = f"https://push2ex.eastmoney.com/getStockChanges?cb=jQuery35103093645168842334_1712143839104&ut=7eea3edcaed734bea9cbfc24409ed989&date={date_str}&dpt=wzchanges&code={code}&market={market_tag}&_=1712143839125"
        response = requests.get(url)
        response.raise_for_status()
        json_data_str = response.text.split("(", 1)[1].rsplit(")", 1)[0]
        data = json.loads(json_data_str)
        logger.debug(f"get_stock_uma, code: {code}, date: {date}, data: {data}")
        if "data" in data and data["data"] is not None:
            result = data["data"]["data"]
            return pd.DataFrame(result)
        return pd.DataFrame()

    def list_us_stock_index_quote(self) -> pd.DataFrame:
        """
        美股指数实时
        :return:
        """
        url = "https://push2.eastmoney.com/api/qt/clist/get?np=1&fltt=1&invt=2&cb=jQuery37107939765842495493_1746197391789&fs=i%3A100.NDX%2Ci%3A100.DJIA%2Ci%3A100.SPX&fields=f12%2Cf13%2Cf14%2Cf292%2Cf1%2Cf2%2Cf4%2Cf3%2Cf152%2Cf17%2Cf28%2Cf15%2Cf16%2Cf18%2Cf7%2Cf124&fid=f3&pn=1&pz=20&po=1&dect=1&ut=fa5fd1943c7b386f172d6893dbfba10b&wbp2u=9651057084417778%7C0%7C1%7C0%7Cweb&_=1746197391793"
        response = requests.get(url)
        response.raise_for_status()
        response_data = json.loads(response.text.split("(", 1)[1].rsplit(")", 1)[0])

        if not response_data.get("data", None):
            return pd.DataFrame()

        # 提取 data['data']['diff'] 部分的内容，包含所有条目
        diff_data = response_data["data"]["diff"]

        # 创建 DataFrame，从上图提取字段
        df = pd.DataFrame(
            [
                {
                    "代码": f"{item['f12']}",
                    "名称": item["f14"],
                    "最新价": item["f2"] / 100,
                    "涨跌额": item["f4"] / 100,
                    "涨跌幅": f"{item['f3'] / 100:.2f}%",
                    "开盘价": item["f17"] / 100,
                    "最高价": item["f15"] / 100,
                    "最低价": item["f16"] / 100,
                    "昨收价": item["f18"] / 100,
                    "振幅": f"{item['f7'] / 100:.2f}%",
                }
                for index, item in enumerate(diff_data)
            ]
        )
        return transform_nan_to_none(df)
    
    @retry_network
    def get_zh_stock_realtime_quotes(self, code: str) -> Optional[pd.DataFrame]:
        """
        获取股票实时行情
        :return: 最新价，涨跌幅，成交量，成交额，最高价，最低价，开盘价，昨收价
        """
        market_str = get_zh_stock_market(code)
        if AStockMarket.SZ.value == market_str:
            market_tag = "0"
        elif AStockMarket.SH.value == market_str:
            market_tag = "1"
        else:
            return None

        url = "https://push2.eastmoney.com/api/qt/stock/get"
        params = {
            "invt": 2,
            "fltt": 1,
            "fields": "f43,f170,f48,f47,f44,f45,f46,f60",
            "ut": "fa5fd1943c7b386f172d6893dbfba10b",
            "secid": f"{market_tag}.{code}",
            "wbp2u": "|0|0|0|web",
            "dect": "1",
        }
        response = requests.get(url, params=params)
        response.raise_for_status()
        resp_data = json.loads(response.text)
        data = resp_data.get("data", {})
        if not data:
            return pd.DataFrame()

        df = pd.DataFrame(
            [
                {
                    "收盘价": data["f43"] / 100,
                    "涨跌幅": data["f170"] / 100,
                    "成交量": data["f47"],
                    "成交额": data["f48"],
                    "最高价": data["f44"] / 100,
                    "最低价": data["f45"] / 100,
                    "开盘价": data["f46"] / 100,
                    "昨收价": data["f60"] / 100,
                }
            ]
        )
        return transform_nan_to_none(df)


if  __name__ == "__main__":
    my_cli = MyClient()
    df = my_cli.get_stock_uma("600760", date.today())
    print(df)