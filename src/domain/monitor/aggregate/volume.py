import threading
import time
from typing import Optional, List

import pandas as pd
from loguru import logger

from src.domain.monitor.aggregate.base import BaseMonitorAggregate
from src.domain.monitor.aggregate.message import message_sender_agg
from src.domain.monitor.aggregate.utils import daily_rate_limiter
from src.domain.monitor.schema import MonitorType
from src.domain.monitor.spec import MonitorObjectSpec
from src.domain.schema import MarketTag
from src.infra.app import app
from src.infra.clients.trading.ak import ak_cli
from src.infra.clients.trading.highlevel import high_level_trading_client
from src.infra.clients.trading.ts import ts_cli
from src.infra.clients.trading.utils import (
    get_datetime_range,
    get_zh_today_trading_time_range,
)


class VolumeMonitorAggregate(BaseMonitorAggregate):

    def __init__(self):
        self.monitor_objects: List[ZhStockVolumeMonitor] = []
        self._lock = threading.Lock()

    def start_all_monitor(self):
        if self.monitor_objects:
            logger.warning("已经有监控在运行，跳过启动")
            return

        objects = self.list_monitor_objects(MonitorType.volume)
        for obj in objects:
            if obj.market_tag != MarketTag.zh_stock.value:
                logger.warning(f"成交量监控只支持A股股票，不支持 {obj.market_tag}")
                continue
            monitor = ZhStockVolumeMonitor(obj.code, obj.name)
            app.submit_concurrent_task(monitor.start_monitor)
            self.monitor_objects.append(monitor)

    def stop_all_monitor(self):
        for monitor in self.monitor_objects:
            monitor.stop_monitor()
        self.monitor_objects = []

    def start_monitor(self, id: int):
        with self._lock:
            obj = self.get_monitor_object_by_id(id)
            if obj is None:
                raise ValueError(f"监控对象 {id} 不存在")

            if obj.monitor_type != MonitorType.volume.value:
                raise ValueError(f"监控对象 {id} 不是成交量监控")

            if obj.market_tag != MarketTag.zh_stock.value:
                raise ValueError(f"成交量监控只支持A股股票，不支持 {obj.market_tag}")

            monitor = ZhStockVolumeMonitor(obj.code, obj.name)
            self.monitor_objects.append(monitor)
            app.submit_concurrent_task(monitor.start_monitor)

    def stop_monitor(self, id: int):
        with self._lock:
            obj = self.get_monitor_object_by_id(id)
            if obj is None:
                raise ValueError(f"监控对象 {id} 不存在")

            if obj.monitor_type != MonitorType.volume.value:
                raise ValueError(f"监控对象 {id} 不是成交量监控")

            for monitor in self.monitor_objects:
                if monitor.code == obj.code:
                    monitor.stop_monitor()
                    self.monitor_objects.remove(monitor)

    def create_object(
        self, code: str, name: str, market_tag: MarketTag
    ) -> Optional[int]:
        if market_tag != MarketTag.zh_stock:
            raise ValueError(f"成交量监控只支持A股股票，不支持 {market_tag}")

        object = self.get_monitor_object_by_info(
            code,
            MonitorType.volume,
            market_tag,
        )
        if object:
            logger.warning(f"量能监控对象 {code},{name} 已经存在，跳过创建")
            return

        object_id = super().create_monitor_object(
            code, name, market_tag, MonitorType.volume
        )
        self.start_monitor(object_id)
        return object_id

    def delete_object(self, id: int):
        self.stop_monitor(id)
        super().delete_monitor_object(id)


class ZhStockVolumeMonitor(MonitorObjectSpec):
    NORMAL_TAG = "正常波动"

    def __init__(self, code: str, name: str):
        self.code = code
        self.name = name
        self._stop_monitor = False
        self._alert_type = self.make_alert_type(code)
        self._daily_limit = 1
        self._monitor_interval = 38
        self._history_5_min_data = self._list_history_5_min_data()
        self._ema_vol: Optional[float] = None
        self._std_vol: Optional[float] = None
        self._30_heavy_volume_mean: Optional[float] = None
        self._90_heavy_volume_mean: Optional[float] = None
        self._init_ema_std()
        self._compute_30_heavy_volume_mean()
        self._compute_90_heavy_volume_mean()

    @property
    def alert_type(self) -> str:
        return self._alert_type

    @classmethod
    def make_alert_type(cls, code: str) -> str:
        return f"zh_volume_monitor_{code}"

    def _make_alert_key(self, row: pd.Series) -> str:
        return f"{self._alert_type}_{row['时间']}"

    def _compute_top10_volume_avg(
        self, df, vol_col="vol", top_n=10, drop_top_k=1
    ) -> float:
        df_sorted = df.sort_values(by=vol_col, ascending=False)
        top_df = df_sorted.head(top_n + drop_top_k)
        clean_df = top_df.iloc[drop_top_k:]  # 剔除前k个极端值
        avg_vol = clean_df[vol_col].mean()
        return round(avg_vol, 2)

    def _compute_30_heavy_volume_mean(self):
        start_date, end_date = get_datetime_range(before_days=35)
        df = ts_cli.list_zh_stock_data_with_minute(
            self.code, start_date, end_date, period="5min"
        )
        # vol 字段的值需要除 100，从股换到手
        df["vol"] = df["vol"] / 100
        self._30_heavy_volume_mean = self._compute_top10_volume_avg(df)
        logger.info(
            f"{self.name}({self.code}) 30_heavy_volume_mean: {self._30_heavy_volume_mean}"
        )

    def _compute_90_heavy_volume_mean(self):
        start_date, end_date = get_datetime_range(before_days=92)
        df = ts_cli.list_zh_stock_data_with_minute(
            self.code, start_date, end_date, period="5min"
        )
        # vol 字段的值需要除 100，从股换到手
        df["vol"] = df["vol"] / 100
        self._90_heavy_volume_mean = self._compute_top10_volume_avg(df)
        logger.info(
            f"{self.name}({self.code}) 90_heavy_volume_mean: {self._90_heavy_volume_mean}"
        )

    def _get_current_stock_price(self) -> Optional[float]:
        data = high_level_trading_client.get_zh_stock_current_min_data(self.code)
        if data is None:
            return None
        return data.close

    def _list_history_5_min_data(
        self,
    ) -> pd.DataFrame:
        start_date, end_date = get_datetime_range(before_days=35)
        df = ak_cli.list_zh_stock_data_with_minute(
            self.code, start_date, end_date, period="5"
        )
        # vol 字段的值需要除 100，从股换到手
        df["vol"] = df["vol"] / 100
        return df

    def _list_today_5_min_data(self) -> pd.DataFrame:
        start_date, end_date = get_zh_today_trading_time_range()
        df = ak_cli.list_zh_stock_data_with_minute(
            self.code, start_date, end_date, period="5"
        )
        # vol 字段的值需要除 100，从股换到手
        df["vol"] = df["vol"] / 100
        return df

    def _init_ema_std(self):
        # 计算 EMA 和 STD（基于当前历史）
        # 用于平滑成交量曲线，反映成交量的“正常趋势水平”。
        self._ema_vol = round(
            self._history_5_min_data["成交量"].ewm(span=1000).mean().iloc[-1], 2
        )
        logger.info(f"{self.name}({self.code}) ema_vol: {self._ema_vol}")
        # 用于判断当前成交量是否显著偏离平均（放量 / 缩量），参考统计波动范围。
        self._std_vol = round(
            self._history_5_min_data["成交量"].rolling(window=1000).std().iloc[-1], 2
        )
        logger.info(f"{self.name}({self.code}) std_vol: {self._std_vol}")

    def _compute_volume_ratio(
        self,
        df_today: pd.DataFrame,
        history_df: pd.DataFrame,  # 改为单个DataFrame
        time_col="时间",
        vol_col="成交量",
    ):
        """
        计算今日每条5分钟K线的"量比"，即相对历史同一时间段的成交量放大倍数。

        参数说明：
        - df_today: 今天的5分钟数据，包含时间和成交量
        - history_df: 历史5分钟数据的DataFrame
        - time_col: 时间列
        - vol_col: 成交量列

        返回值：
        - 增加一列 'volume_ratio'，为量比
        """

        # 构造一个dict：key=时间点，value=过去N天这个时间点的平均成交量
        avg_volume_dict = {}
        # 直接处理单个DataFrame
        for idx, row in history_df.iterrows():
            time_str = row[time_col][-5:]  # 只取 hh:mm 部分
            avg_volume_dict.setdefault(time_str, []).append(row[vol_col])

        # 计算平均值
        for k in avg_volume_dict:
            avg_volume_dict[k] = sum(avg_volume_dict[k]) / len(avg_volume_dict[k])

        # 计算今天每条的量比
        def get_volume_ratio(row):
            time_str = row[time_col][-5:]
            avg = avg_volume_dict.get(time_str)
            if avg and avg > 0:
                return row[vol_col] / avg
            return None

        df_today = df_today.copy()
        df_today["volume_ratio"] = df_today.apply(get_volume_ratio, axis=1)
        return df_today

    def _classify_volume_price(self, row: pd.Series, ema: float, std: float) -> str:
        """
        判断当前K线的量价形态类别

        参数：
        - row: 包含 open、close、vol、volume_ratio 等字段
        - ema: 当前成交量的EMA值
        - std: 当前成交量的STD值

        返回：
        - 类型标签字符串
        """
        open_ = row["开盘"]
        close = row["收盘"]
        vol = row["成交量"]
        vr = row.get("volume_ratio", None)

        # 计算涨速
        if open_ > 0:
            change_rate = (close - open_) / open_
        else:
            return "<font color='purple'> 异常数据 </font>"

        label = self.NORMAL_TAG

        if vol > ema + 2 * std:
            # 设置每日发送限制
            if change_rate > 0.00618:
                label = "<font color='red'>**放量上涨 (趋势确认)**</font>"
            elif change_rate > 0:
                label = "<font color='orange'>**放量滞涨 (主力可能在出货，摸到了关键止盈点)**</font>"
            elif abs(change_rate) < 0.002:
                label = "<font color='blue'>**放量震荡 (可能在洗盘或换手，寻找支撑位)**</font>"
            else:
                label = "<font color='green'>**放量下跌 (关注是否跌破支撑位)**</font>"
        elif vol < ema - 2 * std:
            if change_rate > 0:
                label = "<font color='orange'>**缩量上涨 (可能高度控盘或锁仓，需要关注后续量能)**</font>"

        # 添加量比标签
        if vr is not None and vr > 2:
            label = f"<font color='red'>**[同时段量比倍数放大({round(vr, 2)})]**</font> + {label}"

        return label

    def _process_kline_data(self) -> pd.DataFrame:
        """
        主函数：处理今日数据，计算指标并输出标签

        参数：
        - df_today: 今日实时更新的5分钟数据
        - history_list: 过去N天的5分钟数据列表

        返回：
        - df_today：添加了 volume_ratio, EMA, STD, classify 列
        """
        df = self._list_today_5_min_data()
        if df.empty:
            return df

        # 计算量比
        df = self._compute_volume_ratio(df, self._history_5_min_data)

        # 逐行分类判断
        def classify_row(row):
            return self._classify_volume_price(row, self._ema_vol, self._std_vol)

        df["分类"] = df.apply(classify_row, axis=1)
        return df

    def _monitor(self):
        current_price = self._get_current_stock_price()
        if current_price is None:
            logger.warning(f"获取股票 {self.code} 的当前价格失败")
            return
        logger.debug(f"{self.name}({self.code}) 当前价格: {current_price}")

        df = self._process_kline_data()
        if df.empty:
            logger.warning(f"获取股票 {self.code} 的当前5分钟数据为空")
            return
        # 最后一行，如果不是正常波动，则发送告警
        last_row = df.iloc[-1]
        logger.debug(f"{self.name}({self.code}) 当前5分钟数据:\n{last_row}")
        if last_row["分类"] != self.NORMAL_TAG:
            daily_rate_limiter.set_daily_limit(self._make_alert_key(last_row), limit=1)
            times_30_mean = round(last_row["成交量"] / self._30_heavy_volume_mean, 2)
            times_90_mean = round(last_row["成交量"] / self._90_heavy_volume_mean, 2)

            change_percent = high_level_trading_client.get_zh_stock_current_change(
                self.code
            )
            if change_percent is None:
                logger.warning(f"获取股票 {self.code} 的当前涨跌幅失败")
                return
            if change_percent >= 0:
                change_percent = f"<font color='red'>+{change_percent}%</font>"
            else:
                change_percent = f"<font color='green'>{change_percent}%</font>"
            current_price = f"{last_row['收盘']:.2f} ({change_percent})"
            message_sender_agg.five_min_volume_alert_message(
                code=self.code,
                name=self.name,
                current_price=current_price,
                current_volume=last_row["成交量"],
                volume_30_mean=self._30_heavy_volume_mean,
                volume_90_mean=self._90_heavy_volume_mean,
                volume_times_30=times_30_mean,
                volume_times_90=times_90_mean,
                alert_content=last_row["分类"],
                alert_key=self._make_alert_key(last_row),
            )

    def start_monitor(self):
        logger.info(
            f"开始监控股票 {self.code} 的量价关系, 告警类型: {self._alert_type} "
        )
        while True:
            try:
                self._monitor()
                if self._stop_monitor:
                    break
            except Exception as e:
                logger.exception(f"监控股票 {self.code} 的量价关系时出错: {e}")
            finally:
                time.sleep(self._monitor_interval)

    def stop_monitor(self):
        logger.info(
            f"停止监控股票 {self.code} 的量价关系, 告警类型: {self._alert_type} "
        )
        self._stop_monitor = True
