<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="55a9c41f2b6b446694f1d8ae5e877c12" class="chart-container" style="width:100%; height:400px; "></div>
    <script>
        var chart_55a9c41f2b6b446694f1d8ae5e877c12 = echarts.init(
            document.getElementById('55a9c41f2b6b446694f1d8ae5e877c12'), 'white', {renderer: 'canvas'});
        var option_55a9c41f2b6b446694f1d8ae5e877c12 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K Line",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    5.4,
                    5.39,
                    5.36,
                    5.43
                ],
                [
                    5.38,
                    5.46,
                    5.38,
                    5.47
                ],
                [
                    5.48,
                    5.38,
                    5.36,
                    5.5
                ],
                [
                    5.52,
                    5.36,
                    5.35,
                    5.55
                ],
                [
                    5.37,
                    5.41,
                    5.33,
                    5.43
                ],
                [
                    5.43,
                    5.49,
                    5.37,
                    5.5
                ],
                [
                    5.45,
                    5.29,
                    5.26,
                    5.46
                ],
                [
                    5.28,
                    5.29,
                    5.26,
                    5.34
                ],
                [
                    5.28,
                    5.24,
                    5.21,
                    5.3
                ],
                [
                    5.29,
                    5.23,
                    5.22,
                    5.35
                ],
                [
                    5.18,
                    5.18,
                    5.12,
                    5.2
                ],
                [
                    5.15,
                    5.15,
                    5.13,
                    5.2
                ],
                [
                    5.16,
                    5.09,
                    5.08,
                    5.2
                ],
                [
                    5.1,
                    5.16,
                    5.09,
                    5.19
                ],
                [
                    5.18,
                    5.16,
                    5.09,
                    5.18
                ],
                [
                    5.15,
                    5.15,
                    5.13,
                    5.18
                ],
                [
                    5.14,
                    5.18,
                    5.13,
                    5.22
                ],
                [
                    5.17,
                    5.18,
                    5.12,
                    5.19
                ],
                [
                    5.18,
                    5.06,
                    5.05,
                    5.22
                ],
                [
                    5.06,
                    4.9,
                    4.87,
                    5.11
                ],
                [
                    4.92,
                    4.78,
                    4.74,
                    4.94
                ],
                [
                    4.76,
                    4.78,
                    4.71,
                    4.83
                ],
                [
                    4.78,
                    4.77,
                    4.7,
                    4.79
                ],
                [
                    4.77,
                    4.72,
                    4.63,
                    4.77
                ],
                [
                    4.71,
                    4.69,
                    4.66,
                    4.72
                ],
                [
                    4.68,
                    4.61,
                    4.61,
                    4.7
                ],
                [
                    4.57,
                    4.62,
                    4.56,
                    4.63
                ],
                [
                    4.62,
                    4.74,
                    4.62,
                    4.75
                ],
                [
                    4.74,
                    4.72,
                    4.71,
                    4.77
                ],
                [
                    4.74,
                    4.82,
                    4.74,
                    4.83
                ],
                [
                    4.8,
                    4.8,
                    4.76,
                    4.84
                ],
                [
                    4.83,
                    4.77,
                    4.76,
                    4.85
                ],
                [
                    4.79,
                    4.74,
                    4.68,
                    4.8
                ],
                [
                    4.72,
                    4.68,
                    4.66,
                    4.74
                ],
                [
                    4.72,
                    4.73,
                    4.71,
                    4.83
                ],
                [
                    4.73,
                    4.74,
                    4.71,
                    4.79
                ],
                [
                    4.76,
                    4.76,
                    4.75,
                    4.82
                ],
                [
                    4.8,
                    4.73,
                    4.69,
                    4.81
                ],
                [
                    4.71,
                    4.82,
                    4.7,
                    4.83
                ],
                [
                    4.82,
                    4.87,
                    4.79,
                    4.9
                ],
                [
                    4.87,
                    4.94,
                    4.86,
                    4.97
                ],
                [
                    4.96,
                    4.96,
                    4.9,
                    4.97
                ],
                [
                    4.96,
                    4.97,
                    4.9,
                    4.99
                ],
                [
                    4.96,
                    5.04,
                    4.95,
                    5.11
                ],
                [
                    5.03,
                    5.04,
                    5.0,
                    5.06
                ],
                [
                    5.05,
                    5.28,
                    5.03,
                    5.29
                ],
                [
                    5.24,
                    5.11,
                    5.09,
                    5.26
                ],
                [
                    5.1,
                    5.1,
                    5.06,
                    5.13
                ],
                [
                    5.1,
                    5.05,
                    5.04,
                    5.11
                ],
                [
                    5.08,
                    5.03,
                    5.0,
                    5.09
                ],
                [
                    5.03,
                    5.13,
                    5.03,
                    5.24
                ],
                [
                    5.1,
                    5.08,
                    5.06,
                    5.15
                ],
                [
                    5.08,
                    5.1,
                    5.07,
                    5.12
                ],
                [
                    5.11,
                    5.13,
                    5.06,
                    5.14
                ],
                [
                    5.12,
                    5.06,
                    5.05,
                    5.15
                ],
                [
                    5.08,
                    5.05,
                    5.04,
                    5.09
                ],
                [
                    5.04,
                    5.07,
                    5.01,
                    5.09
                ],
                [
                    5.06,
                    5.06,
                    4.99,
                    5.08
                ],
                [
                    5.08,
                    5.14,
                    5.08,
                    5.19
                ],
                [
                    5.13,
                    5.19,
                    5.13,
                    5.27
                ],
                [
                    5.2,
                    5.14,
                    5.12,
                    5.23
                ],
                [
                    5.1,
                    5.11,
                    5.06,
                    5.11
                ],
                [
                    5.12,
                    5.14,
                    5.08,
                    5.18
                ],
                [
                    5.13,
                    5.09,
                    5.06,
                    5.15
                ],
                [
                    5.09,
                    5.18,
                    5.09,
                    5.18
                ],
                [
                    5.2,
                    5.17,
                    5.15,
                    5.21
                ],
                [
                    5.19,
                    5.12,
                    5.1,
                    5.19
                ],
                [
                    5.13,
                    5.11,
                    5.08,
                    5.13
                ],
                [
                    5.1,
                    5.1,
                    5.08,
                    5.14
                ],
                [
                    5.08,
                    5.1,
                    5.08,
                    5.2
                ],
                [
                    5.11,
                    5.16,
                    5.08,
                    5.22
                ],
                [
                    5.17,
                    5.19,
                    5.15,
                    5.23
                ],
                [
                    5.17,
                    5.26,
                    5.15,
                    5.29
                ],
                [
                    5.23,
                    5.17,
                    5.16,
                    5.27
                ],
                [
                    5.17,
                    5.11,
                    5.09,
                    5.18
                ],
                [
                    5.09,
                    5.04,
                    5.01,
                    5.11
                ],
                [
                    5.05,
                    5.17,
                    5.05,
                    5.21
                ],
                [
                    5.16,
                    5.15,
                    5.13,
                    5.2
                ],
                [
                    5.11,
                    5.16,
                    5.1,
                    5.18
                ],
                [
                    4.9,
                    4.64,
                    4.64,
                    4.96
                ],
                [
                    4.6,
                    4.73,
                    4.6,
                    4.77
                ],
                [
                    4.73,
                    4.83,
                    4.58,
                    4.84
                ],
                [
                    4.89,
                    4.92,
                    4.88,
                    5.01
                ],
                [
                    4.9,
                    4.88,
                    4.88,
                    4.95
                ],
                [
                    4.91,
                    4.88,
                    4.85,
                    4.93
                ],
                [
                    4.88,
                    4.85,
                    4.82,
                    4.88
                ],
                [
                    4.85,
                    4.84,
                    4.78,
                    4.88
                ],
                [
                    4.84,
                    4.83,
                    4.82,
                    4.86
                ],
                [
                    4.83,
                    4.83,
                    4.78,
                    4.84
                ],
                [
                    4.82,
                    4.84,
                    4.8,
                    4.86
                ],
                [
                    4.84,
                    4.94,
                    4.83,
                    4.96
                ],
                [
                    4.92,
                    4.88,
                    4.87,
                    4.95
                ],
                [
                    4.88,
                    4.86,
                    4.84,
                    4.9
                ],
                [
                    4.87,
                    4.9,
                    4.85,
                    4.91
                ],
                [
                    4.91,
                    4.84,
                    4.82,
                    4.92
                ],
                [
                    4.93,
                    5.1,
                    4.92,
                    5.12
                ],
                [
                    5.1,
                    5.1,
                    5.09,
                    5.17
                ],
                [
                    5.12,
                    5.22,
                    5.1,
                    5.24
                ],
                [
                    5.24,
                    5.31,
                    5.22,
                    5.33
                ],
                [
                    5.27,
                    5.21,
                    5.21,
                    5.28
                ],
                [
                    5.22,
                    5.27,
                    5.19,
                    5.28
                ],
                [
                    5.27,
                    5.34,
                    5.25,
                    5.35
                ],
                [
                    5.36,
                    5.3,
                    5.29,
                    5.38
                ],
                [
                    5.3,
                    5.31,
                    5.23,
                    5.32
                ],
                [
                    5.33,
                    5.33,
                    5.32,
                    5.41
                ],
                [
                    5.32,
                    5.28,
                    5.28,
                    5.35
                ],
                [
                    5.29,
                    5.36,
                    5.26,
                    5.36
                ],
                [
                    5.36,
                    5.37,
                    5.33,
                    5.38
                ],
                [
                    5.37,
                    5.36,
                    5.34,
                    5.37
                ],
                [
                    5.35,
                    5.32,
                    5.29,
                    5.36
                ],
                [
                    5.32,
                    5.22,
                    5.22,
                    5.33
                ],
                [
                    5.23,
                    5.27,
                    5.22,
                    5.28
                ],
                [
                    5.25,
                    5.28,
                    5.24,
                    5.29
                ],
                [
                    5.25,
                    5.26,
                    5.24,
                    5.28
                ],
                [
                    5.26,
                    5.22,
                    5.22,
                    5.27
                ],
                [
                    5.22,
                    5.23,
                    5.2,
                    5.25
                ],
                [
                    5.21,
                    5.19,
                    5.16,
                    5.23
                ],
                [
                    5.21,
                    5.22,
                    5.18,
                    5.22
                ],
                [
                    5.22,
                    5.19,
                    5.15,
                    5.24
                ],
                [
                    5.19,
                    5.27,
                    5.18,
                    5.32
                ]
            ],
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "Bottom Signal",
            "symbol": "circle",
            "symbolSize": 10,
            "data": [
                [
                    "2025-06-03",
                    5.06
                ],
                [
                    "2025-06-04",
                    5.08
                ],
                [
                    "2025-06-05",
                    5.05
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "green"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K Line",
                "Bottom Signal"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600704 K \u7ebf\u56fe\u4e0e\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_55a9c41f2b6b446694f1d8ae5e877c12.setOption(option_55a9c41f2b6b446694f1d8ae5e877c12);
            window.addEventListener('resize', function(){
                chart_55a9c41f2b6b446694f1d8ae5e877c12.resize();
            })
    </script>
</body>
</html>
