<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="eec0bfe031fa4ad0b3258f16e82901fe" class="chart-container" style="width:100%; height:400px; "></div>
    <script>
        var chart_eec0bfe031fa4ad0b3258f16e82901fe = echarts.init(
            document.getElementById('eec0bfe031fa4ad0b3258f16e82901fe'), 'white', {renderer: 'canvas'});
        var option_eec0bfe031fa4ad0b3258f16e82901fe = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K Line",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    22.03,
                    22.15,
                    22.01,
                    22.19
                ],
                [
                    22.1,
                    22.21,
                    21.98,
                    22.24
                ],
                [
                    22.45,
                    22.65,
                    22.44,
                    22.88
                ],
                [
                    23.0,
                    22.5,
                    22.47,
                    23.12
                ],
                [
                    22.65,
                    22.68,
                    22.52,
                    22.78
                ],
                [
                    22.78,
                    22.9,
                    22.74,
                    23.12
                ],
                [
                    22.7,
                    22.43,
                    22.37,
                    22.7
                ],
                [
                    22.35,
                    22.17,
                    22.08,
                    22.46
                ],
                [
                    22.2,
                    22.0,
                    21.92,
                    22.23
                ],
                [
                    22.1,
                    21.84,
                    21.8,
                    22.19
                ],
                [
                    21.72,
                    21.53,
                    21.25,
                    21.72
                ],
                [
                    21.4,
                    21.5,
                    21.38,
                    21.63
                ],
                [
                    21.5,
                    21.13,
                    21.07,
                    21.6
                ],
                [
                    21.06,
                    21.18,
                    20.9,
                    21.19
                ],
                [
                    21.18,
                    20.98,
                    20.92,
                    21.2
                ],
                [
                    21.0,
                    21.26,
                    20.99,
                    21.35
                ],
                [
                    21.21,
                    21.3,
                    21.15,
                    21.39
                ],
                [
                    21.3,
                    21.09,
                    21.01,
                    21.3
                ],
                [
                    21.1,
                    20.88,
                    20.74,
                    21.23
                ],
                [
                    20.88,
                    21.09,
                    20.78,
                    21.5
                ],
                [
                    21.37,
                    22.47,
                    21.28,
                    23.01
                ],
                [
                    22.0,
                    22.02,
                    21.34,
                    22.63
                ],
                [
                    21.8,
                    22.23,
                    21.61,
                    22.43
                ],
                [
                    22.0,
                    22.32,
                    21.9,
                    22.74
                ],
                [
                    22.04,
                    22.48,
                    21.8,
                    22.7
                ],
                [
                    22.88,
                    22.07,
                    22.01,
                    23.0
                ],
                [
                    21.76,
                    22.22,
                    21.45,
                    22.48
                ],
                [
                    22.35,
                    22.19,
                    21.78,
                    22.35
                ],
                [
                    22.11,
                    21.89,
                    21.54,
                    22.15
                ],
                [
                    21.99,
                    22.02,
                    21.87,
                    22.5
                ],
                [
                    22.0,
                    21.83,
                    21.74,
                    22.28
                ],
                [
                    21.71,
                    21.63,
                    21.37,
                    21.82
                ],
                [
                    21.55,
                    21.58,
                    21.31,
                    21.72
                ],
                [
                    21.7,
                    21.94,
                    21.56,
                    22.1
                ],
                [
                    21.85,
                    21.66,
                    21.64,
                    22.1
                ],
                [
                    21.58,
                    21.8,
                    21.33,
                    21.8
                ],
                [
                    21.8,
                    21.58,
                    21.52,
                    22.0
                ],
                [
                    22.08,
                    22.76,
                    21.9,
                    22.8
                ],
                [
                    22.63,
                    22.45,
                    22.33,
                    22.63
                ],
                [
                    22.4,
                    22.51,
                    22.28,
                    22.73
                ],
                [
                    22.66,
                    23.16,
                    22.49,
                    23.28
                ],
                [
                    23.6,
                    23.33,
                    23.15,
                    24.06
                ],
                [
                    22.69,
                    22.69,
                    22.49,
                    22.88
                ],
                [
                    22.69,
                    22.56,
                    22.5,
                    22.97
                ],
                [
                    22.74,
                    22.48,
                    22.37,
                    22.8
                ],
                [
                    21.7,
                    21.78,
                    21.63,
                    21.97
                ],
                [
                    21.84,
                    21.78,
                    21.67,
                    22.02
                ],
                [
                    22.1,
                    21.9,
                    21.8,
                    22.16
                ],
                [
                    21.84,
                    21.94,
                    21.7,
                    22.02
                ],
                [
                    21.9,
                    21.56,
                    21.5,
                    21.98
                ],
                [
                    21.44,
                    21.96,
                    21.15,
                    22.11
                ],
                [
                    21.95,
                    21.67,
                    21.64,
                    22.08
                ],
                [
                    21.68,
                    21.73,
                    21.63,
                    22.3
                ],
                [
                    21.73,
                    21.82,
                    21.56,
                    22.13
                ],
                [
                    21.7,
                    21.39,
                    21.38,
                    21.79
                ],
                [
                    21.53,
                    21.34,
                    21.24,
                    21.65
                ],
                [
                    21.43,
                    21.71,
                    21.36,
                    21.73
                ],
                [
                    21.63,
                    21.89,
                    21.6,
                    21.92
                ],
                [
                    21.83,
                    21.89,
                    21.7,
                    21.99
                ],
                [
                    21.73,
                    22.13,
                    21.72,
                    22.45
                ],
                [
                    22.4,
                    22.05,
                    21.92,
                    22.4
                ],
                [
                    21.84,
                    22.11,
                    21.7,
                    22.16
                ],
                [
                    22.12,
                    22.07,
                    22.04,
                    22.39
                ],
                [
                    22.21,
                    22.15,
                    21.93,
                    22.31
                ],
                [
                    22.98,
                    22.34,
                    22.24,
                    23.1
                ],
                [
                    22.3,
                    22.2,
                    22.17,
                    22.51
                ],
                [
                    22.3,
                    22.79,
                    22.2,
                    22.99
                ],
                [
                    22.79,
                    23.3,
                    22.71,
                    23.54
                ],
                [
                    23.58,
                    22.89,
                    22.83,
                    23.73
                ],
                [
                    22.71,
                    22.38,
                    22.23,
                    22.98
                ],
                [
                    22.38,
                    22.18,
                    21.86,
                    22.43
                ],
                [
                    22.1,
                    22.35,
                    21.98,
                    22.8
                ],
                [
                    22.36,
                    22.35,
                    22.17,
                    22.72
                ],
                [
                    22.25,
                    22.35,
                    22.03,
                    22.48
                ],
                [
                    22.9,
                    23.84,
                    22.49,
                    24.14
                ],
                [
                    23.69,
                    24.88,
                    23.51,
                    25.0
                ],
                [
                    24.44,
                    23.91,
                    23.8,
                    24.79
                ],
                [
                    23.74,
                    23.09,
                    22.98,
                    23.84
                ],
                [
                    23.6,
                    24.22,
                    23.17,
                    24.5
                ],
                [
                    21.82,
                    21.8,
                    21.8,
                    22.95
                ],
                [
                    21.45,
                    21.58,
                    20.88,
                    21.98
                ],
                [
                    21.0,
                    21.82,
                    19.8,
                    21.99
                ],
                [
                    22.3,
                    24.0,
                    22.17,
                    24.0
                ],
                [
                    25.38,
                    26.4,
                    25.3,
                    26.4
                ],
                [
                    25.33,
                    26.53,
                    25.33,
                    27.0
                ],
                [
                    26.11,
                    25.79,
                    25.41,
                    26.45
                ],
                [
                    26.52,
                    26.43,
                    25.65,
                    26.72
                ],
                [
                    27.28,
                    25.11,
                    25.05,
                    27.49
                ],
                [
                    25.11,
                    24.47,
                    24.26,
                    25.5
                ],
                [
                    25.11,
                    26.92,
                    24.99,
                    26.92
                ],
                [
                    27.3,
                    28.56,
                    27.04,
                    29.38
                ],
                [
                    26.2,
                    25.9,
                    25.83,
                    27.5
                ],
                [
                    26.97,
                    25.84,
                    25.69,
                    26.99
                ],
                [
                    26.1,
                    24.81,
                    24.52,
                    26.22
                ],
                [
                    24.33,
                    24.19,
                    23.93,
                    24.48
                ],
                [
                    24.49,
                    24.33,
                    24.15,
                    24.69
                ],
                [
                    24.15,
                    24.08,
                    23.85,
                    24.65
                ],
                [
                    24.77,
                    25.8,
                    24.72,
                    26.26
                ],
                [
                    25.1,
                    25.9,
                    25.1,
                    26.0
                ],
                [
                    25.78,
                    25.4,
                    25.27,
                    26.27
                ],
                [
                    24.9,
                    25.12,
                    24.58,
                    25.31
                ],
                [
                    24.31,
                    24.34,
                    23.92,
                    24.45
                ],
                [
                    24.34,
                    24.44,
                    23.94,
                    24.55
                ],
                [
                    24.2,
                    24.06,
                    23.9,
                    24.42
                ],
                [
                    23.62,
                    23.66,
                    23.35,
                    23.89
                ],
                [
                    24.01,
                    23.71,
                    23.65,
                    24.24
                ],
                [
                    23.99,
                    23.93,
                    23.6,
                    24.35
                ],
                [
                    23.76,
                    23.96,
                    23.76,
                    24.16
                ],
                [
                    24.72,
                    25.4,
                    24.24,
                    25.4
                ],
                [
                    24.78,
                    24.25,
                    24.09,
                    25.28
                ],
                [
                    23.81,
                    24.8,
                    23.52,
                    25.46
                ],
                [
                    24.0,
                    24.43,
                    23.92,
                    24.71
                ],
                [
                    24.18,
                    23.7,
                    23.68,
                    24.3
                ],
                [
                    23.6,
                    23.77,
                    23.56,
                    24.0
                ],
                [
                    23.34,
                    23.34,
                    23.01,
                    23.44
                ],
                [
                    23.53,
                    23.24,
                    23.12,
                    23.59
                ],
                [
                    23.82,
                    24.17,
                    23.81,
                    24.62
                ],
                [
                    23.8,
                    24.12,
                    23.8,
                    24.39
                ],
                [
                    24.18,
                    23.91,
                    23.81,
                    24.3
                ],
                [
                    23.85,
                    24.15,
                    23.82,
                    24.43
                ]
            ],
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "Bottom Signal",
            "symbol": "circle",
            "symbolSize": 10,
            "data": [
                [
                    "2025-04-07",
                    21.36
                ],
                [
                    "2025-04-08",
                    20.46
                ],
                [
                    "2025-04-09",
                    19.4
                ],
                [
                    "2025-05-27",
                    23.21
                ],
                [
                    "2025-05-28",
                    23.09
                ],
                [
                    "2025-05-29",
                    22.55
                ],
                [
                    "2025-05-30",
                    22.66
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "green"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K Line",
                "Bottom Signal"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "001337 K \u7ebf\u56fe\u4e0e\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_eec0bfe031fa4ad0b3258f16e82901fe.setOption(option_eec0bfe031fa4ad0b3258f16e82901fe);
            window.addEventListener('resize', function(){
                chart_eec0bfe031fa4ad0b3258f16e82901fe.resize();
            })
    </script>
</body>
</html>
