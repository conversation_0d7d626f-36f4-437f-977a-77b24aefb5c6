<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="88aee2b4c01548fea76e73556b94d6de" class="chart-container" style="width:1200px; height:800px; "></div>
    <script>
        var chart_88aee2b4c01548fea76e73556b94d6de = echarts.init(
            document.getElementById('88aee2b4c01548fea76e73556b94d6de'), 'white', {renderer: 'canvas'});
        var option_88aee2b4c01548fea76e73556b94d6de = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u8d44\u91d1\u6d41\u5165",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0.06,
                0.72,
                1.52,
                1.69,
                2.9,
                3.46,
                7.78,
                25.56
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#ff4444"
            }
        },
        {
            "type": "bar",
            "name": "\u8d44\u91d1\u6d41\u51fa",
            "legendHoverLink": true,
            "data": [
                -275.15,
                -249.15,
                -229.98,
                -228.86,
                -227.52,
                -193.71,
                -116.9,
                -100.73,
                -94.14,
                -92.62,
                -91.82,
                -89.5,
                -87.71,
                -83.06,
                -76.74,
                -75.36,
                -72.69,
                -71.84,
                -60.6,
                -59.71,
                -59.7,
                -56.87,
                -56.45,
                -48.14,
                -46.77,
                -46.56,
                -45.46,
                -45.08,
                -43.6,
                -42.2,
                -41.97,
                -41.88,
                -37.2,
                -34.79,
                -33.65,
                -31.88,
                -30.28,
                -30.15,
                -30.02,
                -29.59,
                -29.57,
                -29.44,
                -29.08,
                -28.62,
                -28.19,
                -27.79,
                -25.18,
                -21.83,
                -20.99,
                -20.58,
                -20.46,
                -20.22,
                -19.03,
                -16.32,
                -15.93,
                -15.66,
                -15.55,
                -15.07,
                -14.64,
                -13.81,
                -13.21,
                -13.07,
                -13.07,
                -13.05,
                -12.07,
                -11.26,
                -9.5,
                -8.56,
                -8.49,
                -8.47,
                -7.2,
                -6.69,
                -6.25,
                -5.87,
                -5.63,
                -3.38,
                -2.77,
                -1.35,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#00aa44"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u8d44\u91d1\u6d41\u5165",
                "\u8d44\u91d1\u6d41\u51fa"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "shadow"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "name": "\u8d44\u91d1\u6d41\u5165 (\u4ebf\u5143)",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "yAxis": [
        {
            "name": "\u884c\u4e1a",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "\u6c7d\u8f66\u96f6\u90e8\u4ef6",
                "\u8f6f\u4ef6\u5f00\u53d1",
                "\u4e13\u7528\u8bbe\u5907",
                "\u901a\u7528\u8bbe\u5907",
                "\u534a\u5bfc\u4f53",
                "\u4e92\u8054\u7f51\u670d\u52a1",
                "\u901a\u4fe1\u8bbe\u5907",
                "\u917f\u9152\u884c\u4e1a",
                "\u822a\u5929\u822a\u7a7a",
                "\u5316\u5b66\u5236\u54c1",
                "\u7535\u529b\u884c\u4e1a",
                "\u5c0f\u91d1\u5c5e",
                "\u6d88\u8d39\u7535\u5b50",
                "\u5316\u5b66\u5236\u836f",
                "\u5149\u5b66\u5149\u7535\u5b50",
                "\u8bc1\u5238",
                "\u519c\u7267\u9972\u6e14",
                "\u5149\u4f0f\u8bbe\u5907",
                "\u7535\u5b50\u5143\u4ef6",
                "\u7535\u673a",
                "\u7535\u7f51\u8bbe\u5907",
                "\u623f\u5730\u4ea7\u5f00\u53d1",
                "\u5316\u5b66\u539f\u6599",
                "\u4e2d\u836f",
                "\u822a\u8fd0\u6e2f\u53e3",
                "\u94f6\u884c",
                "\u5851\u6599\u5236\u54c1",
                "\u73af\u4fdd\u884c\u4e1a",
                "\u7eba\u7ec7\u670d\u88c5",
                "\u4eea\u5668\u4eea\u8868",
                "\u98df\u54c1\u996e\u6599",
                "\u6587\u5316\u4f20\u5a92",
                "\u6c7d\u8f66\u6574\u8f66",
                "\u5546\u4e1a\u767e\u8d27",
                "\u5305\u88c5\u6750\u6599",
                "\u5de5\u7a0b\u673a\u68b0",
                "\u8239\u8236\u5236\u9020",
                "\u7269\u6d41\u884c\u4e1a",
                "\u7535\u6e90\u8bbe\u5907",
                "\u7efc\u5408\u884c\u4e1a",
                "\u533b\u7597\u5668\u68b0",
                "\u5316\u80a5\u884c\u4e1a",
                "\u94a2\u94c1\u884c\u4e1a",
                "\u519c\u836f\u517d\u836f",
                "\u8d38\u6613\u884c\u4e1a",
                "\u8ba1\u7b97\u673a\u8bbe\u5907",
                "\u5bb6\u7528\u8f7b\u5de5",
                "\u65c5\u6e38\u9152\u5e97",
                "\u7f8e\u5bb9\u62a4\u7406",
                "\u6e38\u620f",
                "\u6709\u8272\u91d1\u5c5e",
                "\u751f\u7269\u5236\u54c1",
                "\u5316\u7ea4\u884c\u4e1a",
                "\u9020\u7eb8\u5370\u5237",
                "\u4ea4\u8fd0\u8bbe\u5907",
                "\u901a\u4fe1\u670d\u52a1",
                "\u80fd\u6e90\u91d1\u5c5e",
                "\u5bb6\u7535\u884c\u4e1a",
                "\u591a\u5143\u91d1\u878d",
                "\u71c3\u6c14",
                "\u533b\u7597\u670d\u52a1",
                "\u88c5\u4fee\u88c5\u9970",
                "\u98ce\u7535\u8bbe\u5907",
                "\u4e13\u4e1a\u670d\u52a1",
                "\u5de5\u7a0b\u5efa\u8bbe",
                "\u516c\u7528\u4e8b\u4e1a",
                "\u623f\u5730\u4ea7\u670d\u52a1",
                "\u533b\u836f\u5546\u4e1a",
                "\u73bb\u7483\u73bb\u7ea4",
                "\u91c7\u6398\u884c\u4e1a",
                "\u77f3\u6cb9\u884c\u4e1a",
                "\u7535\u5b50\u5316\u5b66\u54c1",
                "\u6c34\u6ce5\u5efa\u6750",
                "\u6559\u80b2",
                "\u88c5\u4fee\u5efa\u6750",
                "\u6c7d\u8f66\u670d\u52a1",
                "\u822a\u7a7a\u673a\u573a",
                "\u7164\u70ad\u884c\u4e1a",
                "\u5de5\u7a0b\u54a8\u8be2\u670d\u52a1",
                "\u6a61\u80f6\u5236\u54c1",
                "\u975e\u91d1\u5c5e\u6750\u6599",
                "\u94c1\u8def\u516c\u8def",
                "\u73e0\u5b9d\u9996\u9970",
                "\u7535\u6c60",
                "\u8d35\u91d1\u5c5e",
                "\u4fdd\u9669"
            ]
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u8fd125\u65e5\u884c\u4e1a\u4e3b\u529b\u8d44\u91d1\u6d41\u5165 (\u4ebf\u5143)",
            "target": "blank",
            "subtext": "\u7ea2\u8272=\u6d41\u5165\uff0c\u7eff\u8272=\u6d41\u51fa",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ]
};
        chart_88aee2b4c01548fea76e73556b94d6de.setOption(option_88aee2b4c01548fea76e73556b94d6de);
    </script>
</body>
</html>
