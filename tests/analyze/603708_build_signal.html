<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="0fd9622e576f4ac39ab99b9f9f4d6149" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_0fd9622e576f4ac39ab99b9f9f4d6149 = echarts.init(
            document.getElementById('0fd9622e576f4ac39ab99b9f9f4d6149'), 'white', {renderer: 'canvas'});
        var option_0fd9622e576f4ac39ab99b9f9f4d6149 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    11.03,
                    10.98,
                    10.8,
                    11.19
                ],
                [
                    10.98,
                    10.87,
                    10.64,
                    11.01
                ],
                [
                    10.87,
                    11.27,
                    10.75,
                    11.83
                ],
                [
                    11.93,
                    12.4,
                    11.8,
                    12.4
                ],
                [
                    12.73,
                    13.22,
                    12.5,
                    13.6
                ],
                [
                    12.93,
                    14.05,
                    12.5,
                    14.54
                ],
                [
                    13.62,
                    12.72,
                    12.65,
                    13.64
                ],
                [
                    12.63,
                    12.91,
                    12.5,
                    13.49
                ],
                [
                    12.76,
                    12.17,
                    12.01,
                    12.8
                ],
                [
                    12.1,
                    11.96,
                    11.83,
                    12.2
                ],
                [
                    11.88,
                    11.68,
                    11.49,
                    11.97
                ],
                [
                    11.68,
                    11.7,
                    11.63,
                    11.97
                ],
                [
                    11.68,
                    10.81,
                    10.78,
                    11.7
                ],
                [
                    10.81,
                    10.87,
                    10.71,
                    11.02
                ],
                [
                    10.82,
                    10.65,
                    10.3,
                    10.89
                ],
                [
                    10.67,
                    10.95,
                    10.64,
                    11.0
                ],
                [
                    10.95,
                    11.3,
                    10.85,
                    11.58
                ],
                [
                    11.3,
                    11.43,
                    11.15,
                    11.86
                ],
                [
                    11.27,
                    11.39,
                    11.27,
                    11.89
                ],
                [
                    11.27,
                    12.3,
                    11.27,
                    12.3
                ],
                [
                    12.06,
                    11.07,
                    11.07,
                    12.06
                ],
                [
                    10.66,
                    10.39,
                    10.16,
                    10.99
                ],
                [
                    10.37,
                    10.26,
                    9.99,
                    10.44
                ],
                [
                    10.24,
                    10.56,
                    10.1,
                    10.56
                ],
                [
                    10.4,
                    10.37,
                    10.26,
                    10.65
                ],
                [
                    10.3,
                    9.66,
                    9.63,
                    10.38
                ],
                [
                    9.53,
                    9.99,
                    9.27,
                    9.99
                ],
                [
                    9.96,
                    10.37,
                    9.9,
                    10.4
                ],
                [
                    10.38,
                    10.33,
                    10.27,
                    10.51
                ],
                [
                    10.33,
                    10.49,
                    10.29,
                    10.76
                ],
                [
                    10.46,
                    10.32,
                    10.15,
                    10.46
                ],
                [
                    10.38,
                    10.39,
                    10.17,
                    10.56
                ],
                [
                    10.51,
                    10.39,
                    10.26,
                    10.53
                ],
                [
                    10.41,
                    10.04,
                    10.02,
                    10.41
                ],
                [
                    10.27,
                    10.04,
                    10.04,
                    10.33
                ],
                [
                    10.0,
                    10.09,
                    9.91,
                    10.12
                ],
                [
                    10.09,
                    10.02,
                    9.92,
                    10.23
                ],
                [
                    10.06,
                    9.8,
                    9.74,
                    10.08
                ],
                [
                    9.82,
                    9.85,
                    9.6,
                    9.85
                ],
                [
                    9.82,
                    9.84,
                    9.71,
                    9.98
                ],
                [
                    9.89,
                    10.57,
                    9.85,
                    10.66
                ],
                [
                    10.62,
                    10.58,
                    10.28,
                    10.67
                ],
                [
                    10.63,
                    10.77,
                    10.44,
                    10.79
                ],
                [
                    10.76,
                    10.61,
                    10.58,
                    10.82
                ],
                [
                    10.55,
                    10.49,
                    10.4,
                    10.68
                ],
                [
                    10.47,
                    10.48,
                    10.25,
                    10.58
                ],
                [
                    10.44,
                    9.87,
                    9.83,
                    10.45
                ],
                [
                    9.85,
                    10.04,
                    9.75,
                    10.06
                ],
                [
                    10.04,
                    10.19,
                    9.97,
                    10.24
                ],
                [
                    10.21,
                    10.0,
                    9.89,
                    10.22
                ],
                [
                    9.99,
                    10.05,
                    9.95,
                    10.14
                ],
                [
                    9.98,
                    9.98,
                    9.92,
                    10.19
                ],
                [
                    9.97,
                    10.14,
                    9.95,
                    10.17
                ],
                [
                    10.12,
                    10.41,
                    10.11,
                    10.45
                ],
                [
                    10.41,
                    10.06,
                    10.04,
                    10.46
                ],
                [
                    10.15,
                    10.03,
                    9.97,
                    10.27
                ],
                [
                    9.98,
                    10.05,
                    9.92,
                    10.07
                ],
                [
                    10.01,
                    9.98,
                    9.82,
                    10.04
                ],
                [
                    9.96,
                    10.1,
                    9.9,
                    10.14
                ],
                [
                    10.06,
                    10.0,
                    9.95,
                    10.14
                ],
                [
                    9.96,
                    10.03,
                    9.94,
                    10.1
                ],
                [
                    9.92,
                    10.14,
                    9.88,
                    10.14
                ],
                [
                    10.15,
                    10.06,
                    10.06,
                    10.26
                ],
                [
                    10.02,
                    9.98,
                    9.87,
                    10.08
                ],
                [
                    10.02,
                    10.4,
                    10.02,
                    10.4
                ],
                [
                    10.6,
                    10.42,
                    10.39,
                    10.72
                ],
                [
                    10.38,
                    10.35,
                    10.25,
                    10.53
                ],
                [
                    10.36,
                    10.24,
                    10.2,
                    10.39
                ],
                [
                    10.25,
                    10.14,
                    10.1,
                    10.28
                ],
                [
                    10.06,
                    9.97,
                    9.92,
                    10.16
                ],
                [
                    10.01,
                    10.06,
                    9.81,
                    10.11
                ],
                [
                    10.1,
                    10.1,
                    9.94,
                    10.13
                ],
                [
                    10.06,
                    10.13,
                    10.01,
                    10.16
                ],
                [
                    10.12,
                    10.14,
                    10.0,
                    10.2
                ],
                [
                    10.05,
                    9.97,
                    9.93,
                    10.14
                ],
                [
                    9.91,
                    9.79,
                    9.77,
                    9.97
                ],
                [
                    9.78,
                    9.94,
                    9.75,
                    9.95
                ],
                [
                    9.91,
                    9.99,
                    9.86,
                    10.0
                ],
                [
                    9.91,
                    10.28,
                    9.87,
                    10.32
                ],
                [
                    10.12,
                    9.25,
                    9.25,
                    10.12
                ],
                [
                    9.25,
                    10.18,
                    9.22,
                    10.18
                ],
                [
                    10.25,
                    10.67,
                    9.79,
                    10.93
                ],
                [
                    10.66,
                    10.88,
                    10.39,
                    11.15
                ],
                [
                    10.73,
                    10.97,
                    10.72,
                    11.22
                ],
                [
                    10.95,
                    11.42,
                    10.85,
                    11.6
                ],
                [
                    11.42,
                    11.11,
                    11.0,
                    11.6
                ],
                [
                    11.0,
                    10.94,
                    10.78,
                    11.18
                ],
                [
                    10.87,
                    11.07,
                    10.8,
                    11.21
                ],
                [
                    10.97,
                    10.99,
                    10.81,
                    11.15
                ],
                [
                    10.85,
                    11.37,
                    10.85,
                    11.49
                ],
                [
                    11.21,
                    11.56,
                    11.09,
                    11.73
                ],
                [
                    11.49,
                    12.16,
                    10.98,
                    12.6
                ],
                [
                    11.73,
                    11.38,
                    11.3,
                    12.0
                ],
                [
                    11.38,
                    11.4,
                    11.12,
                    11.55
                ],
                [
                    11.37,
                    10.71,
                    10.68,
                    11.39
                ],
                [
                    10.69,
                    10.92,
                    10.53,
                    11.26
                ],
                [
                    10.67,
                    10.56,
                    10.5,
                    10.98
                ],
                [
                    10.56,
                    10.77,
                    10.55,
                    10.79
                ],
                [
                    10.8,
                    10.85,
                    10.7,
                    11.05
                ],
                [
                    10.8,
                    10.92,
                    10.66,
                    11.0
                ],
                [
                    10.92,
                    10.76,
                    10.68,
                    10.93
                ],
                [
                    10.79,
                    10.68,
                    10.61,
                    10.87
                ],
                [
                    10.75,
                    10.61,
                    10.54,
                    10.84
                ],
                [
                    10.67,
                    10.86,
                    10.46,
                    10.92
                ],
                [
                    10.76,
                    10.87,
                    10.75,
                    10.93
                ],
                [
                    10.92,
                    11.08,
                    10.72,
                    11.21
                ],
                [
                    11.03,
                    11.26,
                    10.98,
                    11.27
                ],
                [
                    11.2,
                    11.22,
                    11.09,
                    11.45
                ],
                [
                    11.19,
                    11.08,
                    10.9,
                    11.32
                ],
                [
                    10.96,
                    10.76,
                    10.75,
                    11.13
                ],
                [
                    10.77,
                    10.58,
                    10.54,
                    10.83
                ],
                [
                    10.57,
                    10.63,
                    10.48,
                    10.66
                ],
                [
                    10.62,
                    10.73,
                    10.6,
                    10.76
                ],
                [
                    10.73,
                    10.97,
                    10.63,
                    11.1
                ],
                [
                    10.9,
                    10.89,
                    10.68,
                    11.0
                ],
                [
                    10.82,
                    10.72,
                    10.69,
                    10.96
                ],
                [
                    10.64,
                    10.93,
                    10.64,
                    10.96
                ],
                [
                    10.93,
                    11.02,
                    10.86,
                    11.04
                ],
                [
                    11.02,
                    10.9,
                    10.86,
                    11.08
                ],
                [
                    10.9,
                    10.89,
                    10.74,
                    10.92
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "bar",
            "name": "\u6210\u4ea4\u91cf",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "legendHoverLink": true,
            "data": [
                172038.7,
                93454.89,
                197608.05,
                87268.76,
                317994.15,
                400647.78,
                361298.19,
                251820.9,
                208834.16,
                117461.59,
                95399.11,
                85682.0,
                149766.2,
                80504.9,
                116558.19,
                101867.82,
                180710.82,
                155525.58,
                179742.51,
                289442.64,
                219994.5,
                193671.98,
                124503.5,
                104579.5,
                77828.0,
                103928.85,
                86281.33,
                77006.73,
                57489.4,
                79723.95,
                46557.0,
                46375.9,
                44277.88,
                51928.0,
                46170.9,
                39820.0,
                46840.95,
                47141.84,
                46051.9,
                70658.0,
                139096.0,
                105873.99,
                82743.0,
                62932.46,
                47090.44,
                56692.55,
                77076.44,
                55928.39,
                44735.9,
                54851.18,
                46013.08,
                45602.9,
                42521.95,
                102455.0,
                67157.0,
                54444.0,
                31393.94,
                48032.5,
                41411.66,
                35761.94,
                30643.27,
                37706.27,
                31967.83,
                34546.69,
                83603.28,
                84034.55,
                60281.95,
                47903.3,
                36698.26,
                46859.0,
                66120.2,
                40266.0,
                28823.0,
                33887.0,
                35805.0,
                50203.72,
                32673.0,
                27169.0,
                69995.69,
                96755.6,
                140317.37,
                214384.41,
                227822.72,
                174350.45,
                214962.0,
                170560.79,
                129725.85,
                106563.8,
                73345.25,
                118941.25,
                165982.95,
                278666.96,
                183259.59,
                119552.0,
                126370.82,
                123854.0,
                88939.16,
                64712.07,
                59991.59,
                94306.33,
                47410.47,
                53721.0,
                68109.32,
                93768.0,
                63188.0,
                87651.5,
                75267.66,
                93915.03,
                67197.83,
                48700.0,
                40653.0,
                31457.6,
                41469.97,
                70060.32,
                59676.0,
                33928.0,
                37100.0,
                36501.52,
                40744.75,
                32674.9
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#7f7f7f"
            }
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 2,
            "yAxisIndex": 2,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    -4.63
                ],
                [
                    "2024-12-06",
                    -3.12
                ],
                [
                    "2024-12-09",
                    21.79
                ],
                [
                    "2024-12-10",
                    36.34
                ],
                [
                    "2024-12-11",
                    -5.13
                ],
                [
                    "2024-12-12",
                    2.95
                ],
                [
                    "2024-12-13",
                    -5.47
                ],
                [
                    "2024-12-16",
                    7.48
                ],
                [
                    "2024-12-17",
                    -20.9
                ],
                [
                    "2024-12-18",
                    4.18
                ],
                [
                    "2024-12-19",
                    -6.74
                ],
                [
                    "2024-12-20",
                    -11.52
                ],
                [
                    "2024-12-23",
                    -2.62
                ],
                [
                    "2024-12-24",
                    2.6
                ],
                [
                    "2024-12-25",
                    0.06
                ],
                [
                    "2024-12-26",
                    7.97
                ],
                [
                    "2024-12-27",
                    9.21
                ],
                [
                    "2024-12-30",
                    4.75
                ],
                [
                    "2024-12-31",
                    2.63
                ],
                [
                    "2025-01-02",
                    7.1
                ],
                [
                    "2025-01-03",
                    1.35
                ],
                [
                    "2025-01-06",
                    -15.46
                ],
                [
                    "2025-01-07",
                    -5.43
                ],
                [
                    "2025-01-08",
                    9.13
                ],
                [
                    "2025-01-09",
                    -2.23
                ],
                [
                    "2025-01-10",
                    -13.24
                ],
                [
                    "2025-01-13",
                    2.73
                ],
                [
                    "2025-01-14",
                    5.82
                ],
                [
                    "2025-01-15",
                    -6.44
                ],
                [
                    "2025-01-16",
                    0.61
                ],
                [
                    "2025-01-17",
                    0.36
                ],
                [
                    "2025-01-20",
                    1.8
                ],
                [
                    "2025-01-21",
                    -5.59
                ],
                [
                    "2025-01-22",
                    -9.66
                ],
                [
                    "2025-01-23",
                    -5.37
                ],
                [
                    "2025-01-24",
                    7.09
                ],
                [
                    "2025-01-27",
                    -13.02
                ],
                [
                    "2025-02-05",
                    -10.17
                ],
                [
                    "2025-02-06",
                    -5.86
                ],
                [
                    "2025-02-07",
                    1.43
                ],
                [
                    "2025-02-10",
                    2.12
                ],
                [
                    "2025-02-11",
                    -4.52
                ],
                [
                    "2025-02-12",
                    1.45
                ],
                [
                    "2025-02-13",
                    0.6
                ],
                [
                    "2025-02-14",
                    -10.87
                ],
                [
                    "2025-02-17",
                    -3.34
                ],
                [
                    "2025-02-18",
                    -1.98
                ],
                [
                    "2025-02-19",
                    -2.12
                ],
                [
                    "2025-02-20",
                    4.18
                ],
                [
                    "2025-02-21",
                    -9.02
                ],
                [
                    "2025-02-24",
                    -5.87
                ],
                [
                    "2025-02-25",
                    7.21
                ],
                [
                    "2025-02-26",
                    3.73
                ],
                [
                    "2025-02-27",
                    4.22
                ],
                [
                    "2025-02-28",
                    -2.66
                ],
                [
                    "2025-03-03",
                    -14.07
                ],
                [
                    "2025-03-04",
                    -1.01
                ],
                [
                    "2025-03-05",
                    3.63
                ],
                [
                    "2025-03-06",
                    -9.74
                ],
                [
                    "2025-03-07",
                    -3.71
                ],
                [
                    "2025-03-10",
                    -4.79
                ],
                [
                    "2025-03-11",
                    -3.66
                ],
                [
                    "2025-03-12",
                    -4.71
                ],
                [
                    "2025-03-13",
                    -4.41
                ],
                [
                    "2025-03-14",
                    5.7
                ],
                [
                    "2025-03-17",
                    -13.67
                ],
                [
                    "2025-03-18",
                    1.26
                ],
                [
                    "2025-03-19",
                    -8.43
                ],
                [
                    "2025-03-20",
                    -12.4
                ],
                [
                    "2025-03-21",
                    -1.08
                ],
                [
                    "2025-03-24",
                    1.5
                ],
                [
                    "2025-03-25",
                    5.63
                ],
                [
                    "2025-03-26",
                    -3.61
                ],
                [
                    "2025-03-27",
                    -7.81
                ],
                [
                    "2025-03-28",
                    -1.46
                ],
                [
                    "2025-03-31",
                    -3.73
                ],
                [
                    "2025-04-01",
                    1.15
                ],
                [
                    "2025-04-02",
                    4.05
                ],
                [
                    "2025-04-03",
                    4.23
                ],
                [
                    "2025-04-07",
                    1.83
                ],
                [
                    "2025-04-08",
                    4.28
                ],
                [
                    "2025-04-09",
                    -0.07
                ],
                [
                    "2025-04-10",
                    1.38
                ],
                [
                    "2025-04-11",
                    2.94
                ],
                [
                    "2025-04-14",
                    0.02
                ],
                [
                    "2025-04-15",
                    -4.9
                ],
                [
                    "2025-04-16",
                    -8.6
                ],
                [
                    "2025-04-17",
                    2.49
                ],
                [
                    "2025-04-18",
                    -7.42
                ],
                [
                    "2025-04-21",
                    12.66
                ],
                [
                    "2025-04-22",
                    -2.07
                ],
                [
                    "2025-04-23",
                    5.3
                ],
                [
                    "2025-04-24",
                    -6.76
                ],
                [
                    "2025-04-25",
                    -4.79
                ],
                [
                    "2025-04-28",
                    1.33
                ],
                [
                    "2025-04-29",
                    2.45
                ],
                [
                    "2025-04-30",
                    -15.05
                ],
                [
                    "2025-05-06",
                    -4.85
                ],
                [
                    "2025-05-07",
                    -9.71
                ],
                [
                    "2025-05-08",
                    15.03
                ],
                [
                    "2025-05-09",
                    -3.71
                ],
                [
                    "2025-05-12",
                    -21.5
                ],
                [
                    "2025-05-13",
                    -0.66
                ],
                [
                    "2025-05-14",
                    3.29
                ],
                [
                    "2025-05-15",
                    0.23
                ],
                [
                    "2025-05-16",
                    13.24
                ],
                [
                    "2025-05-19",
                    -3.89
                ],
                [
                    "2025-05-20",
                    -2.87
                ],
                [
                    "2025-05-21",
                    -16.41
                ],
                [
                    "2025-05-22",
                    -13.74
                ],
                [
                    "2025-05-23",
                    4.63
                ],
                [
                    "2025-05-26",
                    -5.82
                ],
                [
                    "2025-05-27",
                    -9.83
                ],
                [
                    "2025-05-28",
                    16.21
                ],
                [
                    "2025-05-29",
                    -9.52
                ],
                [
                    "2025-05-30",
                    -1.25
                ],
                [
                    "2025-06-03",
                    -3.25
                ],
                [
                    "2025-06-04",
                    -7.06
                ],
                [
                    "2025-06-05",
                    4.3
                ],
                [
                    "2025-06-06",
                    -1.12
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#5470C6"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u5165\u573a\u4fe1\u53f7",
            "xAxisIndex": 3,
            "yAxisIndex": 3,
            "symbol": "triangle",
            "symbolSize": 12,
            "data": [
                [
                    "2024-12-05",
                    null
                ],
                [
                    "2024-12-06",
                    null
                ],
                [
                    "2024-12-09",
                    null
                ],
                [
                    "2024-12-10",
                    null
                ],
                [
                    "2024-12-11",
                    null
                ],
                [
                    "2024-12-12",
                    null
                ],
                [
                    "2024-12-13",
                    null
                ],
                [
                    "2024-12-16",
                    null
                ],
                [
                    "2024-12-17",
                    null
                ],
                [
                    "2024-12-18",
                    null
                ],
                [
                    "2024-12-19",
                    null
                ],
                [
                    "2024-12-20",
                    null
                ],
                [
                    "2024-12-23",
                    null
                ],
                [
                    "2024-12-24",
                    null
                ],
                [
                    "2024-12-25",
                    null
                ],
                [
                    "2024-12-26",
                    10.64
                ],
                [
                    "2024-12-27",
                    10.85
                ],
                [
                    "2024-12-30",
                    null
                ],
                [
                    "2024-12-31",
                    null
                ],
                [
                    "2025-01-02",
                    null
                ],
                [
                    "2025-01-03",
                    null
                ],
                [
                    "2025-01-06",
                    null
                ],
                [
                    "2025-01-07",
                    null
                ],
                [
                    "2025-01-08",
                    null
                ],
                [
                    "2025-01-09",
                    null
                ],
                [
                    "2025-01-10",
                    null
                ],
                [
                    "2025-01-13",
                    null
                ],
                [
                    "2025-01-14",
                    null
                ],
                [
                    "2025-01-15",
                    null
                ],
                [
                    "2025-01-16",
                    null
                ],
                [
                    "2025-01-17",
                    null
                ],
                [
                    "2025-01-20",
                    null
                ],
                [
                    "2025-01-21",
                    null
                ],
                [
                    "2025-01-22",
                    null
                ],
                [
                    "2025-01-23",
                    null
                ],
                [
                    "2025-01-24",
                    null
                ],
                [
                    "2025-01-27",
                    null
                ],
                [
                    "2025-02-05",
                    null
                ],
                [
                    "2025-02-06",
                    null
                ],
                [
                    "2025-02-07",
                    null
                ],
                [
                    "2025-02-10",
                    null
                ],
                [
                    "2025-02-11",
                    null
                ],
                [
                    "2025-02-12",
                    null
                ],
                [
                    "2025-02-13",
                    null
                ],
                [
                    "2025-02-14",
                    null
                ],
                [
                    "2025-02-17",
                    null
                ],
                [
                    "2025-02-18",
                    null
                ],
                [
                    "2025-02-19",
                    null
                ],
                [
                    "2025-02-20",
                    null
                ],
                [
                    "2025-02-21",
                    null
                ],
                [
                    "2025-02-24",
                    null
                ],
                [
                    "2025-02-25",
                    9.92
                ],
                [
                    "2025-02-26",
                    9.95
                ],
                [
                    "2025-02-27",
                    null
                ],
                [
                    "2025-02-28",
                    null
                ],
                [
                    "2025-03-03",
                    null
                ],
                [
                    "2025-03-04",
                    null
                ],
                [
                    "2025-03-05",
                    null
                ],
                [
                    "2025-03-06",
                    null
                ],
                [
                    "2025-03-07",
                    null
                ],
                [
                    "2025-03-10",
                    null
                ],
                [
                    "2025-03-11",
                    null
                ],
                [
                    "2025-03-12",
                    null
                ],
                [
                    "2025-03-13",
                    null
                ],
                [
                    "2025-03-14",
                    null
                ],
                [
                    "2025-03-17",
                    null
                ],
                [
                    "2025-03-18",
                    null
                ],
                [
                    "2025-03-19",
                    null
                ],
                [
                    "2025-03-20",
                    null
                ],
                [
                    "2025-03-21",
                    null
                ],
                [
                    "2025-03-24",
                    null
                ],
                [
                    "2025-03-25",
                    null
                ],
                [
                    "2025-03-26",
                    null
                ],
                [
                    "2025-03-27",
                    null
                ],
                [
                    "2025-03-28",
                    null
                ],
                [
                    "2025-03-31",
                    null
                ],
                [
                    "2025-04-01",
                    null
                ],
                [
                    "2025-04-02",
                    9.86
                ],
                [
                    "2025-04-03",
                    null
                ],
                [
                    "2025-04-07",
                    null
                ],
                [
                    "2025-04-08",
                    null
                ],
                [
                    "2025-04-09",
                    null
                ],
                [
                    "2025-04-10",
                    null
                ],
                [
                    "2025-04-11",
                    null
                ],
                [
                    "2025-04-14",
                    null
                ],
                [
                    "2025-04-15",
                    null
                ],
                [
                    "2025-04-16",
                    null
                ],
                [
                    "2025-04-17",
                    null
                ],
                [
                    "2025-04-18",
                    null
                ],
                [
                    "2025-04-21",
                    null
                ],
                [
                    "2025-04-22",
                    null
                ],
                [
                    "2025-04-23",
                    null
                ],
                [
                    "2025-04-24",
                    null
                ],
                [
                    "2025-04-25",
                    null
                ],
                [
                    "2025-04-28",
                    null
                ],
                [
                    "2025-04-29",
                    null
                ],
                [
                    "2025-04-30",
                    null
                ],
                [
                    "2025-05-06",
                    null
                ],
                [
                    "2025-05-07",
                    null
                ],
                [
                    "2025-05-08",
                    null
                ],
                [
                    "2025-05-09",
                    null
                ],
                [
                    "2025-05-12",
                    null
                ],
                [
                    "2025-05-13",
                    null
                ],
                [
                    "2025-05-14",
                    null
                ],
                [
                    "2025-05-15",
                    null
                ],
                [
                    "2025-05-16",
                    null
                ],
                [
                    "2025-05-19",
                    null
                ],
                [
                    "2025-05-20",
                    null
                ],
                [
                    "2025-05-21",
                    null
                ],
                [
                    "2025-05-22",
                    null
                ],
                [
                    "2025-05-23",
                    null
                ],
                [
                    "2025-05-26",
                    null
                ],
                [
                    "2025-05-27",
                    null
                ],
                [
                    "2025-05-28",
                    null
                ],
                [
                    "2025-05-29",
                    null
                ],
                [
                    "2025-05-30",
                    null
                ],
                [
                    "2025-06-03",
                    null
                ],
                [
                    "2025-06-04",
                    null
                ],
                [
                    "2025-06-05",
                    null
                ],
                [
                    "2025-06-06",
                    null
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "purple"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u6210\u4ea4\u91cf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u5165\u573a\u4fe1\u53f7"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 2,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "position": "right",
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603708 \u4e3b\u529b\u5165\u573a\u4fe1\u53f7 K \u7ebf\u56fe",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 50,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 50,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 50,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 50,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "5%",
            "right": "8%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "50%",
            "right": "8%",
            "height": "15%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "70%",
            "right": "8%",
            "height": "15%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "5%",
            "right": "8%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_0fd9622e576f4ac39ab99b9f9f4d6149.setOption(option_0fd9622e576f4ac39ab99b9f9f4d6149);
            window.addEventListener('resize', function(){
                chart_0fd9622e576f4ac39ab99b9f9f4d6149.resize();
            })
    </script>
</body>
</html>
