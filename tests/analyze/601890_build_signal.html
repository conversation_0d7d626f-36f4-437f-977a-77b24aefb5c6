<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="f1c237886e4843359af4536870e2c2cf" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_f1c237886e4843359af4536870e2c2cf = echarts.init(
            document.getElementById('f1c237886e4843359af4536870e2c2cf'), 'white', {renderer: 'canvas'});
        var option_f1c237886e4843359af4536870e2c2cf = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    8.58,
                    8.45,
                    8.38,
                    8.65
                ],
                [
                    8.43,
                    8.49,
                    8.35,
                    8.59
                ],
                [
                    8.48,
                    8.47,
                    8.35,
                    8.51
                ],
                [
                    8.44,
                    8.47,
                    8.37,
                    8.58
                ],
                [
                    8.61,
                    8.42,
                    8.4,
                    8.68
                ],
                [
                    8.48,
                    8.81,
                    8.45,
                    8.88
                ],
                [
                    8.8,
                    8.72,
                    8.64,
                    8.88
                ],
                [
                    8.71,
                    8.54,
                    8.53,
                    8.78
                ],
                [
                    8.53,
                    8.46,
                    8.4,
                    8.6
                ],
                [
                    8.43,
                    8.23,
                    8.2,
                    8.54
                ],
                [
                    8.24,
                    8.23,
                    8.17,
                    8.36
                ],
                [
                    8.14,
                    8.23,
                    8.06,
                    8.27
                ],
                [
                    8.3,
                    8.21,
                    8.16,
                    8.32
                ],
                [
                    8.22,
                    8.01,
                    7.97,
                    8.23
                ],
                [
                    7.99,
                    8.05,
                    7.99,
                    8.12
                ],
                [
                    8.01,
                    7.87,
                    7.81,
                    8.08
                ],
                [
                    7.88,
                    7.92,
                    7.86,
                    7.97
                ],
                [
                    7.95,
                    8.08,
                    7.95,
                    8.12
                ],
                [
                    8.07,
                    8.02,
                    7.97,
                    8.17
                ],
                [
                    8.03,
                    7.79,
                    7.77,
                    8.03
                ],
                [
                    7.71,
                    7.5,
                    7.42,
                    7.79
                ],
                [
                    7.61,
                    7.64,
                    7.55,
                    8.08
                ],
                [
                    7.4,
                    7.34,
                    7.27,
                    7.53
                ],
                [
                    7.32,
                    7.42,
                    7.28,
                    7.43
                ],
                [
                    7.39,
                    7.41,
                    7.21,
                    7.48
                ],
                [
                    7.33,
                    7.38,
                    7.33,
                    7.47
                ],
                [
                    7.36,
                    7.27,
                    7.27,
                    7.42
                ],
                [
                    7.22,
                    7.32,
                    7.19,
                    7.38
                ],
                [
                    7.48,
                    7.9,
                    7.36,
                    7.93
                ],
                [
                    7.93,
                    7.84,
                    7.74,
                    7.94
                ],
                [
                    7.89,
                    7.89,
                    7.8,
                    7.96
                ],
                [
                    8.19,
                    7.83,
                    7.81,
                    8.68
                ],
                [
                    7.87,
                    7.81,
                    7.72,
                    7.93
                ],
                [
                    7.85,
                    7.83,
                    7.7,
                    7.87
                ],
                [
                    7.81,
                    7.74,
                    7.7,
                    7.83
                ],
                [
                    7.81,
                    7.68,
                    7.67,
                    7.87
                ],
                [
                    7.63,
                    7.73,
                    7.61,
                    7.74
                ],
                [
                    7.76,
                    7.64,
                    7.6,
                    7.79
                ],
                [
                    7.65,
                    7.69,
                    7.63,
                    7.72
                ],
                [
                    7.66,
                    7.85,
                    7.6,
                    7.85
                ],
                [
                    7.87,
                    7.85,
                    7.77,
                    7.9
                ],
                [
                    7.85,
                    7.86,
                    7.78,
                    7.88
                ],
                [
                    7.86,
                    7.83,
                    7.78,
                    7.87
                ],
                [
                    7.8,
                    7.85,
                    7.78,
                    7.86
                ],
                [
                    7.86,
                    7.76,
                    7.76,
                    7.88
                ],
                [
                    7.77,
                    7.74,
                    7.71,
                    7.8
                ],
                [
                    7.74,
                    7.74,
                    7.66,
                    7.78
                ],
                [
                    7.7,
                    7.51,
                    7.47,
                    7.74
                ],
                [
                    7.48,
                    7.53,
                    7.48,
                    7.57
                ],
                [
                    7.52,
                    7.5,
                    7.46,
                    7.54
                ],
                [
                    7.52,
                    7.53,
                    7.45,
                    7.53
                ],
                [
                    7.54,
                    7.56,
                    7.49,
                    7.61
                ],
                [
                    7.52,
                    7.54,
                    7.46,
                    7.61
                ],
                [
                    7.54,
                    7.63,
                    7.53,
                    7.65
                ],
                [
                    7.63,
                    7.58,
                    7.48,
                    7.66
                ],
                [
                    7.53,
                    7.47,
                    7.44,
                    7.6
                ],
                [
                    7.46,
                    7.42,
                    7.41,
                    7.54
                ],
                [
                    7.43,
                    7.54,
                    7.41,
                    7.56
                ],
                [
                    7.5,
                    7.46,
                    7.42,
                    7.53
                ],
                [
                    7.48,
                    7.53,
                    7.44,
                    7.54
                ],
                [
                    7.52,
                    7.54,
                    7.47,
                    7.65
                ],
                [
                    7.54,
                    7.71,
                    7.51,
                    7.74
                ],
                [
                    7.65,
                    7.78,
                    7.61,
                    7.78
                ],
                [
                    7.78,
                    7.86,
                    7.71,
                    7.97
                ],
                [
                    7.87,
                    8.25,
                    7.71,
                    8.35
                ],
                [
                    8.25,
                    8.46,
                    8.17,
                    8.65
                ],
                [
                    8.76,
                    8.36,
                    8.32,
                    9.05
                ],
                [
                    8.3,
                    8.81,
                    8.3,
                    9.02
                ],
                [
                    8.6,
                    8.58,
                    8.5,
                    8.86
                ],
                [
                    8.59,
                    9.44,
                    8.5,
                    9.44
                ],
                [
                    9.89,
                    10.38,
                    9.7,
                    10.38
                ],
                [
                    11.1,
                    11.42,
                    10.54,
                    11.42
                ],
                [
                    10.58,
                    10.28,
                    10.28,
                    10.98
                ],
                [
                    9.58,
                    10.02,
                    9.58,
                    10.62
                ],
                [
                    9.72,
                    10.58,
                    9.18,
                    10.93
                ],
                [
                    10.1,
                    10.31,
                    9.66,
                    10.76
                ],
                [
                    10.0,
                    9.6,
                    9.3,
                    10.11
                ],
                [
                    9.53,
                    9.29,
                    9.28,
                    9.8
                ],
                [
                    9.14,
                    9.2,
                    9.04,
                    9.42
                ],
                [
                    9.01,
                    8.87,
                    8.71,
                    9.18
                ],
                [
                    8.3,
                    7.98,
                    7.98,
                    8.49
                ],
                [
                    7.88,
                    8.08,
                    7.88,
                    8.24
                ],
                [
                    7.88,
                    8.7,
                    7.57,
                    8.86
                ],
                [
                    8.6,
                    8.71,
                    8.57,
                    8.92
                ],
                [
                    8.6,
                    8.68,
                    8.57,
                    8.82
                ],
                [
                    8.7,
                    8.87,
                    8.7,
                    8.98
                ],
                [
                    8.87,
                    8.7,
                    8.6,
                    8.9
                ],
                [
                    8.65,
                    8.53,
                    8.42,
                    8.9
                ],
                [
                    8.45,
                    8.61,
                    8.45,
                    8.85
                ],
                [
                    8.55,
                    9.02,
                    8.55,
                    9.13
                ],
                [
                    8.93,
                    9.04,
                    8.73,
                    9.1
                ],
                [
                    8.97,
                    8.74,
                    8.73,
                    8.97
                ],
                [
                    8.83,
                    8.82,
                    8.69,
                    8.9
                ],
                [
                    8.8,
                    8.61,
                    8.57,
                    8.8
                ],
                [
                    8.59,
                    8.59,
                    8.55,
                    8.69
                ],
                [
                    8.55,
                    8.49,
                    8.46,
                    8.65
                ],
                [
                    8.49,
                    8.55,
                    8.39,
                    8.6
                ],
                [
                    8.54,
                    8.6,
                    8.53,
                    8.65
                ],
                [
                    8.65,
                    8.89,
                    8.64,
                    8.89
                ],
                [
                    9.0,
                    9.3,
                    8.89,
                    9.42
                ],
                [
                    9.13,
                    9.29,
                    8.98,
                    9.35
                ],
                [
                    9.34,
                    9.1,
                    9.04,
                    9.4
                ],
                [
                    9.08,
                    9.34,
                    9.0,
                    9.6
                ],
                [
                    9.43,
                    9.03,
                    9.01,
                    9.43
                ],
                [
                    9.02,
                    9.04,
                    8.96,
                    9.09
                ],
                [
                    8.98,
                    8.75,
                    8.75,
                    9.01
                ],
                [
                    8.82,
                    8.83,
                    8.78,
                    9.2
                ],
                [
                    8.83,
                    8.84,
                    8.69,
                    8.88
                ],
                [
                    8.85,
                    8.92,
                    8.75,
                    8.95
                ],
                [
                    8.86,
                    8.74,
                    8.72,
                    8.88
                ],
                [
                    8.72,
                    8.52,
                    8.51,
                    8.72
                ],
                [
                    8.51,
                    8.46,
                    8.45,
                    8.62
                ],
                [
                    8.46,
                    8.54,
                    8.45,
                    8.55
                ],
                [
                    8.52,
                    8.42,
                    8.38,
                    8.53
                ],
                [
                    8.43,
                    8.77,
                    8.34,
                    8.85
                ],
                [
                    8.62,
                    8.73,
                    8.56,
                    8.8
                ],
                [
                    8.66,
                    8.64,
                    8.52,
                    8.7
                ],
                [
                    8.58,
                    8.73,
                    8.5,
                    8.78
                ],
                [
                    8.73,
                    9.06,
                    8.68,
                    9.06
                ],
                [
                    8.99,
                    8.88,
                    8.85,
                    9.06
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "bar",
            "name": "\u6210\u4ea4\u91cf",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "legendHoverLink": true,
            "data": [
                480415.31,
                398551.33,
                280817.34,
                250172.0,
                374183.93,
                627415.26,
                347134.1,
                279569.96,
                205912.25,
                243447.0,
                141998.0,
                155337.0,
                144473.0,
                209328.13,
                122156.1,
                154694.0,
                140946.71,
                167891.64,
                152762.05,
                179160.0,
                186786.15,
                385397.95,
                229568.63,
                114898.35,
                148858.01,
                126294.14,
                107605.53,
                94196.05,
                348825.52,
                163169.0,
                167337.0,
                551662.2,
                255535.69,
                146680.72,
                102652.0,
                153522.0,
                103497.4,
                105524.15,
                96799.2,
                129147.15,
                187743.88,
                147168.55,
                112899.0,
                112559.61,
                134041.04,
                95135.01,
                137227.86,
                172005.05,
                122539.4,
                142224.74,
                135390.32,
                119590.58,
                105516.6,
                122336.9,
                125848.1,
                121208.45,
                112336.0,
                121826.95,
                97535.25,
                111781.83,
                172079.76,
                234913.25,
                216861.97,
                291135.35,
                631748.3,
                980070.05,
                1027344.04,
                978521.38,
                616310.43,
                1037636.46,
                1691029.67,
                2611894.53,
                1567877.33,
                1614106.39,
                2078312.07,
                1846962.71,
                1248533.35,
                1019422.51,
                775482.01,
                941709.06,
                647459.15,
                891250.43,
                1185649.53,
                980450.39,
                545893.7,
                518600.11,
                352939.86,
                363828.51,
                408579.77,
                859577.18,
                614450.05,
                553830.25,
                476599.0,
                373207.87,
                234450.12,
                210037.65,
                209268.02,
                225690.05,
                394087.21,
                1043395.28,
                676716.15,
                450808.27,
                885593.22,
                661756.12,
                340242.0,
                345735.0,
                399234.02,
                268452.0,
                282542.0,
                275101.0,
                292007.05,
                207336.02,
                135844.2,
                207952.0,
                656472.64,
                447703.65,
                292617.0,
                285087.0,
                725898.29,
                471086.04
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#7f7f7f"
            }
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 2,
            "yAxisIndex": 2,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-04",
                    -13.88
                ],
                [
                    "2024-12-05",
                    2.04
                ],
                [
                    "2024-12-06",
                    -4.19
                ],
                [
                    "2024-12-09",
                    3.23
                ],
                [
                    "2024-12-10",
                    -4.39
                ],
                [
                    "2024-12-11",
                    14.34
                ],
                [
                    "2024-12-12",
                    -10.0
                ],
                [
                    "2024-12-13",
                    -3.65
                ],
                [
                    "2024-12-16",
                    -11.5
                ],
                [
                    "2024-12-17",
                    -16.32
                ],
                [
                    "2024-12-18",
                    3.39
                ],
                [
                    "2024-12-19",
                    -7.22
                ],
                [
                    "2024-12-20",
                    3.42
                ],
                [
                    "2024-12-23",
                    -5.02
                ],
                [
                    "2024-12-24",
                    -6.88
                ],
                [
                    "2024-12-25",
                    -12.55
                ],
                [
                    "2024-12-26",
                    -8.22
                ],
                [
                    "2024-12-27",
                    0.1
                ],
                [
                    "2024-12-30",
                    3.22
                ],
                [
                    "2024-12-31",
                    -6.0
                ],
                [
                    "2025-01-02",
                    -9.23
                ],
                [
                    "2025-01-03",
                    7.54
                ],
                [
                    "2025-01-06",
                    -11.82
                ],
                [
                    "2025-01-07",
                    -3.6
                ],
                [
                    "2025-01-08",
                    -5.35
                ],
                [
                    "2025-01-09",
                    -7.62
                ],
                [
                    "2025-01-10",
                    -6.01
                ],
                [
                    "2025-01-13",
                    -3.78
                ],
                [
                    "2025-01-14",
                    1.95
                ],
                [
                    "2025-01-15",
                    0.69
                ],
                [
                    "2025-01-16",
                    5.69
                ],
                [
                    "2025-01-17",
                    7.66
                ],
                [
                    "2025-01-20",
                    -12.23
                ],
                [
                    "2025-01-21",
                    -2.08
                ],
                [
                    "2025-01-22",
                    -11.23
                ],
                [
                    "2025-01-23",
                    -7.28
                ],
                [
                    "2025-01-24",
                    -8.31
                ],
                [
                    "2025-01-27",
                    -10.18
                ],
                [
                    "2025-02-05",
                    -8.33
                ],
                [
                    "2025-02-06",
                    7.7
                ],
                [
                    "2025-02-07",
                    0.87
                ],
                [
                    "2025-02-10",
                    -9.74
                ],
                [
                    "2025-02-11",
                    -5.68
                ],
                [
                    "2025-02-12",
                    -4.53
                ],
                [
                    "2025-02-13",
                    1.76
                ],
                [
                    "2025-02-14",
                    -16.08
                ],
                [
                    "2025-02-17",
                    -2.01
                ],
                [
                    "2025-02-18",
                    -21.4
                ],
                [
                    "2025-02-19",
                    -15.85
                ],
                [
                    "2025-02-20",
                    -7.89
                ],
                [
                    "2025-02-21",
                    -5.24
                ],
                [
                    "2025-02-24",
                    -0.02
                ],
                [
                    "2025-02-25",
                    -6.52
                ],
                [
                    "2025-02-26",
                    -2.62
                ],
                [
                    "2025-02-27",
                    2.44
                ],
                [
                    "2025-02-28",
                    -7.95
                ],
                [
                    "2025-03-03",
                    -4.2
                ],
                [
                    "2025-03-04",
                    -5.91
                ],
                [
                    "2025-03-05",
                    -5.75
                ],
                [
                    "2025-03-06",
                    -3.96
                ],
                [
                    "2025-03-07",
                    -1.9
                ],
                [
                    "2025-03-10",
                    13.31
                ],
                [
                    "2025-03-11",
                    -0.84
                ],
                [
                    "2025-03-12",
                    9.14
                ],
                [
                    "2025-03-13",
                    14.31
                ],
                [
                    "2025-03-14",
                    1.92
                ],
                [
                    "2025-03-17",
                    -6.83
                ],
                [
                    "2025-03-18",
                    4.08
                ],
                [
                    "2025-03-19",
                    -7.82
                ],
                [
                    "2025-03-20",
                    24.64
                ],
                [
                    "2025-03-21",
                    1.58
                ],
                [
                    "2025-03-24",
                    -8.28
                ],
                [
                    "2025-03-25",
                    -5.92
                ],
                [
                    "2025-03-26",
                    -5.53
                ],
                [
                    "2025-03-27",
                    3.37
                ],
                [
                    "2025-03-28",
                    -5.62
                ],
                [
                    "2025-03-31",
                    0.78
                ],
                [
                    "2025-04-01",
                    -1.43
                ],
                [
                    "2025-04-02",
                    -0.25
                ],
                [
                    "2025-04-03",
                    -9.27
                ],
                [
                    "2025-04-07",
                    -13.56
                ],
                [
                    "2025-04-08",
                    -5.28
                ],
                [
                    "2025-04-09",
                    2.08
                ],
                [
                    "2025-04-10",
                    -8.6
                ],
                [
                    "2025-04-11",
                    -3.42
                ],
                [
                    "2025-04-14",
                    -0.73
                ],
                [
                    "2025-04-15",
                    -6.85
                ],
                [
                    "2025-04-16",
                    -4.11
                ],
                [
                    "2025-04-17",
                    3.67
                ],
                [
                    "2025-04-18",
                    7.04
                ],
                [
                    "2025-04-21",
                    1.69
                ],
                [
                    "2025-04-22",
                    -20.08
                ],
                [
                    "2025-04-23",
                    0.78
                ],
                [
                    "2025-04-24",
                    -15.59
                ],
                [
                    "2025-04-25",
                    -4.7
                ],
                [
                    "2025-04-28",
                    -6.28
                ],
                [
                    "2025-04-29",
                    -1.17
                ],
                [
                    "2025-04-30",
                    3.2
                ],
                [
                    "2025-05-06",
                    -1.11
                ],
                [
                    "2025-05-07",
                    0.16
                ],
                [
                    "2025-05-08",
                    -0.32
                ],
                [
                    "2025-05-09",
                    -6.16
                ],
                [
                    "2025-05-12",
                    -1.37
                ],
                [
                    "2025-05-13",
                    -20.23
                ],
                [
                    "2025-05-14",
                    -1.89
                ],
                [
                    "2025-05-15",
                    -10.53
                ],
                [
                    "2025-05-16",
                    3.22
                ],
                [
                    "2025-05-19",
                    -5.85
                ],
                [
                    "2025-05-20",
                    6.19
                ],
                [
                    "2025-05-21",
                    -9.07
                ],
                [
                    "2025-05-22",
                    -6.82
                ],
                [
                    "2025-05-23",
                    -3.98
                ],
                [
                    "2025-05-26",
                    -1.27
                ],
                [
                    "2025-05-27",
                    -11.19
                ],
                [
                    "2025-05-28",
                    10.77
                ],
                [
                    "2025-05-29",
                    -7.11
                ],
                [
                    "2025-05-30",
                    0.49
                ],
                [
                    "2025-06-03",
                    10.81
                ],
                [
                    "2025-06-04",
                    5.16
                ],
                [
                    "2025-06-05",
                    -11.56
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#5470C6"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u5165\u573a\u4fe1\u53f7",
            "xAxisIndex": 3,
            "yAxisIndex": 3,
            "symbol": "triangle",
            "symbolSize": 12,
            "data": [
                [
                    "2024-12-04",
                    null
                ],
                [
                    "2024-12-05",
                    null
                ],
                [
                    "2024-12-06",
                    null
                ],
                [
                    "2024-12-09",
                    null
                ],
                [
                    "2024-12-10",
                    null
                ],
                [
                    "2024-12-11",
                    null
                ],
                [
                    "2024-12-12",
                    null
                ],
                [
                    "2024-12-13",
                    null
                ],
                [
                    "2024-12-16",
                    null
                ],
                [
                    "2024-12-17",
                    null
                ],
                [
                    "2024-12-18",
                    null
                ],
                [
                    "2024-12-19",
                    null
                ],
                [
                    "2024-12-20",
                    null
                ],
                [
                    "2024-12-23",
                    null
                ],
                [
                    "2024-12-24",
                    null
                ],
                [
                    "2024-12-25",
                    null
                ],
                [
                    "2024-12-26",
                    null
                ],
                [
                    "2024-12-27",
                    null
                ],
                [
                    "2024-12-30",
                    null
                ],
                [
                    "2024-12-31",
                    null
                ],
                [
                    "2025-01-02",
                    null
                ],
                [
                    "2025-01-03",
                    null
                ],
                [
                    "2025-01-06",
                    null
                ],
                [
                    "2025-01-07",
                    null
                ],
                [
                    "2025-01-08",
                    null
                ],
                [
                    "2025-01-09",
                    null
                ],
                [
                    "2025-01-10",
                    null
                ],
                [
                    "2025-01-13",
                    null
                ],
                [
                    "2025-01-14",
                    null
                ],
                [
                    "2025-01-15",
                    null
                ],
                [
                    "2025-01-16",
                    7.8
                ],
                [
                    "2025-01-17",
                    null
                ],
                [
                    "2025-01-20",
                    null
                ],
                [
                    "2025-01-21",
                    null
                ],
                [
                    "2025-01-22",
                    null
                ],
                [
                    "2025-01-23",
                    null
                ],
                [
                    "2025-01-24",
                    null
                ],
                [
                    "2025-01-27",
                    null
                ],
                [
                    "2025-02-05",
                    null
                ],
                [
                    "2025-02-06",
                    null
                ],
                [
                    "2025-02-07",
                    null
                ],
                [
                    "2025-02-10",
                    null
                ],
                [
                    "2025-02-11",
                    null
                ],
                [
                    "2025-02-12",
                    null
                ],
                [
                    "2025-02-13",
                    null
                ],
                [
                    "2025-02-14",
                    null
                ],
                [
                    "2025-02-17",
                    null
                ],
                [
                    "2025-02-18",
                    null
                ],
                [
                    "2025-02-19",
                    null
                ],
                [
                    "2025-02-20",
                    null
                ],
                [
                    "2025-02-21",
                    null
                ],
                [
                    "2025-02-24",
                    null
                ],
                [
                    "2025-02-25",
                    null
                ],
                [
                    "2025-02-26",
                    null
                ],
                [
                    "2025-02-27",
                    null
                ],
                [
                    "2025-02-28",
                    null
                ],
                [
                    "2025-03-03",
                    null
                ],
                [
                    "2025-03-04",
                    null
                ],
                [
                    "2025-03-05",
                    null
                ],
                [
                    "2025-03-06",
                    null
                ],
                [
                    "2025-03-07",
                    null
                ],
                [
                    "2025-03-10",
                    null
                ],
                [
                    "2025-03-11",
                    null
                ],
                [
                    "2025-03-12",
                    null
                ],
                [
                    "2025-03-13",
                    null
                ],
                [
                    "2025-03-14",
                    null
                ],
                [
                    "2025-03-17",
                    null
                ],
                [
                    "2025-03-18",
                    null
                ],
                [
                    "2025-03-19",
                    null
                ],
                [
                    "2025-03-20",
                    null
                ],
                [
                    "2025-03-21",
                    null
                ],
                [
                    "2025-03-24",
                    null
                ],
                [
                    "2025-03-25",
                    null
                ],
                [
                    "2025-03-26",
                    null
                ],
                [
                    "2025-03-27",
                    null
                ],
                [
                    "2025-03-28",
                    null
                ],
                [
                    "2025-03-31",
                    null
                ],
                [
                    "2025-04-01",
                    null
                ],
                [
                    "2025-04-02",
                    null
                ],
                [
                    "2025-04-03",
                    null
                ],
                [
                    "2025-04-07",
                    null
                ],
                [
                    "2025-04-08",
                    null
                ],
                [
                    "2025-04-09",
                    null
                ],
                [
                    "2025-04-10",
                    null
                ],
                [
                    "2025-04-11",
                    null
                ],
                [
                    "2025-04-14",
                    null
                ],
                [
                    "2025-04-15",
                    null
                ],
                [
                    "2025-04-16",
                    null
                ],
                [
                    "2025-04-17",
                    null
                ],
                [
                    "2025-04-18",
                    null
                ],
                [
                    "2025-04-21",
                    null
                ],
                [
                    "2025-04-22",
                    null
                ],
                [
                    "2025-04-23",
                    null
                ],
                [
                    "2025-04-24",
                    null
                ],
                [
                    "2025-04-25",
                    null
                ],
                [
                    "2025-04-28",
                    null
                ],
                [
                    "2025-04-29",
                    null
                ],
                [
                    "2025-04-30",
                    null
                ],
                [
                    "2025-05-06",
                    null
                ],
                [
                    "2025-05-07",
                    null
                ],
                [
                    "2025-05-08",
                    null
                ],
                [
                    "2025-05-09",
                    null
                ],
                [
                    "2025-05-12",
                    null
                ],
                [
                    "2025-05-13",
                    null
                ],
                [
                    "2025-05-14",
                    null
                ],
                [
                    "2025-05-15",
                    null
                ],
                [
                    "2025-05-16",
                    null
                ],
                [
                    "2025-05-19",
                    null
                ],
                [
                    "2025-05-20",
                    null
                ],
                [
                    "2025-05-21",
                    null
                ],
                [
                    "2025-05-22",
                    null
                ],
                [
                    "2025-05-23",
                    null
                ],
                [
                    "2025-05-26",
                    null
                ],
                [
                    "2025-05-27",
                    null
                ],
                [
                    "2025-05-28",
                    null
                ],
                [
                    "2025-05-29",
                    null
                ],
                [
                    "2025-05-30",
                    null
                ],
                [
                    "2025-06-03",
                    null
                ],
                [
                    "2025-06-04",
                    null
                ],
                [
                    "2025-06-05",
                    null
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "purple"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u6210\u4ea4\u91cf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u5165\u573a\u4fe1\u53f7"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 2,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "position": "right",
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "601890 \u4e3b\u529b\u5165\u573a\u4fe1\u53f7 K \u7ebf\u56fe",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 50,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 50,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 50,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 50,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "5%",
            "right": "8%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "50%",
            "right": "8%",
            "height": "15%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "70%",
            "right": "8%",
            "height": "15%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "5%",
            "right": "8%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_f1c237886e4843359af4536870e2c2cf.setOption(option_f1c237886e4843359af4536870e2c2cf);
            window.addEventListener('resize', function(){
                chart_f1c237886e4843359af4536870e2c2cf.resize();
            })
    </script>
</body>
</html>
