<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="96d055324219460cae342931684b4674" class="chart-container" style="width:100%; height:400px; "></div>
    <script>
        var chart_96d055324219460cae342931684b4674 = echarts.init(
            document.getElementById('96d055324219460cae342931684b4674'), 'white', {renderer: 'canvas'});
        var option_96d055324219460cae342931684b4674 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K Line",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    11.03,
                    10.98,
                    10.8,
                    11.19
                ],
                [
                    10.98,
                    10.87,
                    10.64,
                    11.01
                ],
                [
                    10.87,
                    11.27,
                    10.75,
                    11.83
                ],
                [
                    11.93,
                    12.4,
                    11.8,
                    12.4
                ],
                [
                    12.73,
                    13.22,
                    12.5,
                    13.6
                ],
                [
                    12.93,
                    14.05,
                    12.5,
                    14.54
                ],
                [
                    13.62,
                    12.72,
                    12.65,
                    13.64
                ],
                [
                    12.63,
                    12.91,
                    12.5,
                    13.49
                ],
                [
                    12.76,
                    12.17,
                    12.01,
                    12.8
                ],
                [
                    12.1,
                    11.96,
                    11.83,
                    12.2
                ],
                [
                    11.88,
                    11.68,
                    11.49,
                    11.97
                ],
                [
                    11.68,
                    11.7,
                    11.63,
                    11.97
                ],
                [
                    11.68,
                    10.81,
                    10.78,
                    11.7
                ],
                [
                    10.81,
                    10.87,
                    10.71,
                    11.02
                ],
                [
                    10.82,
                    10.65,
                    10.3,
                    10.89
                ],
                [
                    10.67,
                    10.95,
                    10.64,
                    11.0
                ],
                [
                    10.95,
                    11.3,
                    10.85,
                    11.58
                ],
                [
                    11.3,
                    11.43,
                    11.15,
                    11.86
                ],
                [
                    11.27,
                    11.39,
                    11.27,
                    11.89
                ],
                [
                    11.27,
                    12.3,
                    11.27,
                    12.3
                ],
                [
                    12.06,
                    11.07,
                    11.07,
                    12.06
                ],
                [
                    10.66,
                    10.39,
                    10.16,
                    10.99
                ],
                [
                    10.37,
                    10.26,
                    9.99,
                    10.44
                ],
                [
                    10.24,
                    10.56,
                    10.1,
                    10.56
                ],
                [
                    10.4,
                    10.37,
                    10.26,
                    10.65
                ],
                [
                    10.3,
                    9.66,
                    9.63,
                    10.38
                ],
                [
                    9.53,
                    9.99,
                    9.27,
                    9.99
                ],
                [
                    9.96,
                    10.37,
                    9.9,
                    10.4
                ],
                [
                    10.38,
                    10.33,
                    10.27,
                    10.51
                ],
                [
                    10.33,
                    10.49,
                    10.29,
                    10.76
                ],
                [
                    10.46,
                    10.32,
                    10.15,
                    10.46
                ],
                [
                    10.38,
                    10.39,
                    10.17,
                    10.56
                ],
                [
                    10.51,
                    10.39,
                    10.26,
                    10.53
                ],
                [
                    10.41,
                    10.04,
                    10.02,
                    10.41
                ],
                [
                    10.27,
                    10.04,
                    10.04,
                    10.33
                ],
                [
                    10.0,
                    10.09,
                    9.91,
                    10.12
                ],
                [
                    10.09,
                    10.02,
                    9.92,
                    10.23
                ],
                [
                    10.06,
                    9.8,
                    9.74,
                    10.08
                ],
                [
                    9.82,
                    9.85,
                    9.6,
                    9.85
                ],
                [
                    9.82,
                    9.84,
                    9.71,
                    9.98
                ],
                [
                    9.89,
                    10.57,
                    9.85,
                    10.66
                ],
                [
                    10.62,
                    10.58,
                    10.28,
                    10.67
                ],
                [
                    10.63,
                    10.77,
                    10.44,
                    10.79
                ],
                [
                    10.76,
                    10.61,
                    10.58,
                    10.82
                ],
                [
                    10.55,
                    10.49,
                    10.4,
                    10.68
                ],
                [
                    10.47,
                    10.48,
                    10.25,
                    10.58
                ],
                [
                    10.44,
                    9.87,
                    9.83,
                    10.45
                ],
                [
                    9.85,
                    10.04,
                    9.75,
                    10.06
                ],
                [
                    10.04,
                    10.19,
                    9.97,
                    10.24
                ],
                [
                    10.21,
                    10.0,
                    9.89,
                    10.22
                ],
                [
                    9.99,
                    10.05,
                    9.95,
                    10.14
                ],
                [
                    9.98,
                    9.98,
                    9.92,
                    10.19
                ],
                [
                    9.97,
                    10.14,
                    9.95,
                    10.17
                ],
                [
                    10.12,
                    10.41,
                    10.11,
                    10.45
                ],
                [
                    10.41,
                    10.06,
                    10.04,
                    10.46
                ],
                [
                    10.15,
                    10.03,
                    9.97,
                    10.27
                ],
                [
                    9.98,
                    10.05,
                    9.92,
                    10.07
                ],
                [
                    10.01,
                    9.98,
                    9.82,
                    10.04
                ],
                [
                    9.96,
                    10.1,
                    9.9,
                    10.14
                ],
                [
                    10.06,
                    10.0,
                    9.95,
                    10.14
                ],
                [
                    9.96,
                    10.03,
                    9.94,
                    10.1
                ],
                [
                    9.92,
                    10.14,
                    9.88,
                    10.14
                ],
                [
                    10.15,
                    10.06,
                    10.06,
                    10.26
                ],
                [
                    10.02,
                    9.98,
                    9.87,
                    10.08
                ],
                [
                    10.02,
                    10.4,
                    10.02,
                    10.4
                ],
                [
                    10.6,
                    10.42,
                    10.39,
                    10.72
                ],
                [
                    10.38,
                    10.35,
                    10.25,
                    10.53
                ],
                [
                    10.36,
                    10.24,
                    10.2,
                    10.39
                ],
                [
                    10.25,
                    10.14,
                    10.1,
                    10.28
                ],
                [
                    10.06,
                    9.97,
                    9.92,
                    10.16
                ],
                [
                    10.01,
                    10.06,
                    9.81,
                    10.11
                ],
                [
                    10.1,
                    10.1,
                    9.94,
                    10.13
                ],
                [
                    10.06,
                    10.13,
                    10.01,
                    10.16
                ],
                [
                    10.12,
                    10.14,
                    10.0,
                    10.2
                ],
                [
                    10.05,
                    9.97,
                    9.93,
                    10.14
                ],
                [
                    9.91,
                    9.79,
                    9.77,
                    9.97
                ],
                [
                    9.78,
                    9.94,
                    9.75,
                    9.95
                ],
                [
                    9.91,
                    9.99,
                    9.86,
                    10.0
                ],
                [
                    9.91,
                    10.28,
                    9.87,
                    10.32
                ],
                [
                    10.12,
                    9.25,
                    9.25,
                    10.12
                ],
                [
                    9.25,
                    10.18,
                    9.22,
                    10.18
                ],
                [
                    10.25,
                    10.67,
                    9.79,
                    10.93
                ],
                [
                    10.66,
                    10.88,
                    10.39,
                    11.15
                ],
                [
                    10.73,
                    10.97,
                    10.72,
                    11.22
                ],
                [
                    10.95,
                    11.42,
                    10.85,
                    11.6
                ],
                [
                    11.42,
                    11.11,
                    11.0,
                    11.6
                ],
                [
                    11.0,
                    10.94,
                    10.78,
                    11.18
                ],
                [
                    10.87,
                    11.07,
                    10.8,
                    11.21
                ],
                [
                    10.97,
                    10.99,
                    10.81,
                    11.15
                ],
                [
                    10.85,
                    11.37,
                    10.85,
                    11.49
                ],
                [
                    11.21,
                    11.56,
                    11.09,
                    11.73
                ],
                [
                    11.49,
                    12.16,
                    10.98,
                    12.6
                ],
                [
                    11.73,
                    11.38,
                    11.3,
                    12.0
                ],
                [
                    11.38,
                    11.4,
                    11.12,
                    11.55
                ],
                [
                    11.37,
                    10.71,
                    10.68,
                    11.39
                ],
                [
                    10.69,
                    10.92,
                    10.53,
                    11.26
                ],
                [
                    10.67,
                    10.56,
                    10.5,
                    10.98
                ],
                [
                    10.56,
                    10.77,
                    10.55,
                    10.79
                ],
                [
                    10.8,
                    10.85,
                    10.7,
                    11.05
                ],
                [
                    10.8,
                    10.92,
                    10.66,
                    11.0
                ],
                [
                    10.92,
                    10.76,
                    10.68,
                    10.93
                ],
                [
                    10.79,
                    10.68,
                    10.61,
                    10.87
                ],
                [
                    10.75,
                    10.61,
                    10.54,
                    10.84
                ],
                [
                    10.67,
                    10.86,
                    10.46,
                    10.92
                ],
                [
                    10.76,
                    10.87,
                    10.75,
                    10.93
                ],
                [
                    10.92,
                    11.08,
                    10.72,
                    11.21
                ],
                [
                    11.03,
                    11.26,
                    10.98,
                    11.27
                ],
                [
                    11.2,
                    11.22,
                    11.09,
                    11.45
                ],
                [
                    11.19,
                    11.08,
                    10.9,
                    11.32
                ],
                [
                    10.96,
                    10.76,
                    10.75,
                    11.13
                ],
                [
                    10.77,
                    10.58,
                    10.54,
                    10.83
                ],
                [
                    10.57,
                    10.63,
                    10.48,
                    10.66
                ],
                [
                    10.62,
                    10.73,
                    10.6,
                    10.76
                ],
                [
                    10.73,
                    10.97,
                    10.63,
                    11.1
                ],
                [
                    10.9,
                    10.89,
                    10.68,
                    11.0
                ],
                [
                    10.82,
                    10.72,
                    10.69,
                    10.96
                ],
                [
                    10.64,
                    10.93,
                    10.64,
                    10.96
                ],
                [
                    10.93,
                    11.02,
                    10.86,
                    11.04
                ],
                [
                    11.02,
                    10.9,
                    10.86,
                    11.08
                ],
                [
                    10.9,
                    10.89,
                    10.74,
                    10.92
                ]
            ],
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "Bottom Signal",
            "symbol": "circle",
            "symbolSize": 10,
            "data": [
                [
                    "2025-03-05",
                    9.62
                ],
                [
                    "2025-04-07",
                    9.06
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "green"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K Line",
                "Bottom Signal"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "603708 K \u7ebf\u56fe\u4e0e\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_96d055324219460cae342931684b4674.setOption(option_96d055324219460cae342931684b4674);
            window.addEventListener('resize', function(){
                chart_96d055324219460cae342931684b4674.resize();
            })
    </script>
</body>
</html>
