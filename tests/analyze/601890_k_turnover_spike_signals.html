<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="d3fb99a13c3c49a2b5071f1f1715bd36" class="chart-container" style="width:100%; height:450px; "></div>
    <script>
        var chart_d3fb99a13c3c49a2b5071f1f1715bd36 = echarts.init(
            document.getElementById('d3fb99a13c3c49a2b5071f1f1715bd36'), 'white', {renderer: 'canvas'});
        var option_d3fb99a13c3c49a2b5071f1f1715bd36 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    8.08,
                    8.78,
                    8.08,
                    8.78
                ],
                [
                    8.8,
                    8.72,
                    8.57,
                    9.08
                ],
                [
                    8.58,
                    8.45,
                    8.38,
                    8.65
                ],
                [
                    8.43,
                    8.49,
                    8.35,
                    8.59
                ],
                [
                    8.48,
                    8.47,
                    8.35,
                    8.51
                ],
                [
                    8.44,
                    8.47,
                    8.37,
                    8.58
                ],
                [
                    8.61,
                    8.42,
                    8.4,
                    8.68
                ],
                [
                    8.48,
                    8.81,
                    8.45,
                    8.88
                ],
                [
                    8.8,
                    8.72,
                    8.64,
                    8.88
                ],
                [
                    8.71,
                    8.54,
                    8.53,
                    8.78
                ],
                [
                    8.53,
                    8.46,
                    8.4,
                    8.6
                ],
                [
                    8.43,
                    8.23,
                    8.2,
                    8.54
                ],
                [
                    8.24,
                    8.23,
                    8.17,
                    8.36
                ],
                [
                    8.14,
                    8.23,
                    8.06,
                    8.27
                ],
                [
                    8.3,
                    8.21,
                    8.16,
                    8.32
                ],
                [
                    8.22,
                    8.01,
                    7.97,
                    8.23
                ],
                [
                    7.99,
                    8.05,
                    7.99,
                    8.12
                ],
                [
                    8.01,
                    7.87,
                    7.81,
                    8.08
                ],
                [
                    7.88,
                    7.92,
                    7.86,
                    7.97
                ],
                [
                    7.95,
                    8.08,
                    7.95,
                    8.12
                ],
                [
                    8.07,
                    8.02,
                    7.97,
                    8.17
                ],
                [
                    8.03,
                    7.79,
                    7.77,
                    8.03
                ],
                [
                    7.71,
                    7.5,
                    7.42,
                    7.79
                ],
                [
                    7.61,
                    7.64,
                    7.55,
                    8.08
                ],
                [
                    7.4,
                    7.34,
                    7.27,
                    7.53
                ],
                [
                    7.32,
                    7.42,
                    7.28,
                    7.43
                ],
                [
                    7.39,
                    7.41,
                    7.21,
                    7.48
                ],
                [
                    7.33,
                    7.38,
                    7.33,
                    7.47
                ],
                [
                    7.36,
                    7.27,
                    7.27,
                    7.42
                ],
                [
                    7.22,
                    7.32,
                    7.19,
                    7.38
                ],
                [
                    7.48,
                    7.9,
                    7.36,
                    7.93
                ],
                [
                    7.93,
                    7.84,
                    7.74,
                    7.94
                ],
                [
                    7.89,
                    7.89,
                    7.8,
                    7.96
                ],
                [
                    8.19,
                    7.83,
                    7.81,
                    8.68
                ],
                [
                    7.87,
                    7.81,
                    7.72,
                    7.93
                ],
                [
                    7.85,
                    7.83,
                    7.7,
                    7.87
                ],
                [
                    7.81,
                    7.74,
                    7.7,
                    7.83
                ],
                [
                    7.81,
                    7.68,
                    7.67,
                    7.87
                ],
                [
                    7.63,
                    7.73,
                    7.61,
                    7.74
                ],
                [
                    7.76,
                    7.64,
                    7.6,
                    7.79
                ],
                [
                    7.65,
                    7.69,
                    7.63,
                    7.72
                ],
                [
                    7.66,
                    7.85,
                    7.6,
                    7.85
                ],
                [
                    7.87,
                    7.85,
                    7.77,
                    7.9
                ],
                [
                    7.85,
                    7.86,
                    7.78,
                    7.88
                ],
                [
                    7.86,
                    7.83,
                    7.78,
                    7.87
                ],
                [
                    7.8,
                    7.85,
                    7.78,
                    7.86
                ],
                [
                    7.86,
                    7.76,
                    7.76,
                    7.88
                ],
                [
                    7.77,
                    7.74,
                    7.71,
                    7.8
                ],
                [
                    7.74,
                    7.74,
                    7.66,
                    7.78
                ],
                [
                    7.7,
                    7.51,
                    7.47,
                    7.74
                ],
                [
                    7.48,
                    7.53,
                    7.48,
                    7.57
                ],
                [
                    7.52,
                    7.5,
                    7.46,
                    7.54
                ],
                [
                    7.52,
                    7.53,
                    7.45,
                    7.53
                ],
                [
                    7.54,
                    7.56,
                    7.49,
                    7.61
                ],
                [
                    7.52,
                    7.54,
                    7.46,
                    7.61
                ],
                [
                    7.54,
                    7.63,
                    7.53,
                    7.65
                ],
                [
                    7.63,
                    7.58,
                    7.48,
                    7.66
                ],
                [
                    7.53,
                    7.47,
                    7.44,
                    7.6
                ],
                [
                    7.46,
                    7.42,
                    7.41,
                    7.54
                ],
                [
                    7.43,
                    7.54,
                    7.41,
                    7.56
                ],
                [
                    7.5,
                    7.46,
                    7.42,
                    7.53
                ],
                [
                    7.48,
                    7.53,
                    7.44,
                    7.54
                ],
                [
                    7.52,
                    7.54,
                    7.47,
                    7.65
                ],
                [
                    7.54,
                    7.71,
                    7.51,
                    7.74
                ],
                [
                    7.65,
                    7.78,
                    7.61,
                    7.78
                ],
                [
                    7.78,
                    7.86,
                    7.71,
                    7.97
                ],
                [
                    7.87,
                    8.25,
                    7.71,
                    8.35
                ],
                [
                    8.25,
                    8.46,
                    8.17,
                    8.65
                ],
                [
                    8.76,
                    8.36,
                    8.32,
                    9.05
                ],
                [
                    8.3,
                    8.81,
                    8.3,
                    9.02
                ],
                [
                    8.6,
                    8.58,
                    8.5,
                    8.86
                ],
                [
                    8.59,
                    9.44,
                    8.5,
                    9.44
                ],
                [
                    9.89,
                    10.38,
                    9.7,
                    10.38
                ],
                [
                    11.1,
                    11.42,
                    10.54,
                    11.42
                ],
                [
                    10.58,
                    10.28,
                    10.28,
                    10.98
                ],
                [
                    9.58,
                    10.02,
                    9.58,
                    10.62
                ],
                [
                    9.72,
                    10.58,
                    9.18,
                    10.93
                ],
                [
                    10.1,
                    10.31,
                    9.66,
                    10.76
                ],
                [
                    10.0,
                    9.6,
                    9.3,
                    10.11
                ],
                [
                    9.53,
                    9.29,
                    9.28,
                    9.8
                ],
                [
                    9.14,
                    9.2,
                    9.04,
                    9.42
                ],
                [
                    9.01,
                    8.87,
                    8.71,
                    9.18
                ],
                [
                    8.3,
                    7.98,
                    7.98,
                    8.49
                ],
                [
                    7.88,
                    8.08,
                    7.88,
                    8.24
                ],
                [
                    7.88,
                    8.7,
                    7.57,
                    8.86
                ],
                [
                    8.6,
                    8.71,
                    8.57,
                    8.92
                ],
                [
                    8.6,
                    8.68,
                    8.57,
                    8.82
                ],
                [
                    8.7,
                    8.87,
                    8.7,
                    8.98
                ],
                [
                    8.87,
                    8.7,
                    8.6,
                    8.9
                ],
                [
                    8.65,
                    8.53,
                    8.42,
                    8.9
                ],
                [
                    8.45,
                    8.61,
                    8.45,
                    8.85
                ],
                [
                    8.55,
                    9.02,
                    8.55,
                    9.13
                ],
                [
                    8.93,
                    9.04,
                    8.73,
                    9.1
                ],
                [
                    8.97,
                    8.74,
                    8.73,
                    8.97
                ],
                [
                    8.83,
                    8.82,
                    8.69,
                    8.9
                ],
                [
                    8.8,
                    8.61,
                    8.57,
                    8.8
                ],
                [
                    8.59,
                    8.59,
                    8.55,
                    8.69
                ],
                [
                    8.55,
                    8.49,
                    8.46,
                    8.65
                ],
                [
                    8.49,
                    8.55,
                    8.39,
                    8.6
                ],
                [
                    8.54,
                    8.6,
                    8.53,
                    8.65
                ],
                [
                    8.65,
                    8.89,
                    8.64,
                    8.89
                ],
                [
                    9.0,
                    9.3,
                    8.89,
                    9.42
                ],
                [
                    9.13,
                    9.29,
                    8.98,
                    9.35
                ],
                [
                    9.34,
                    9.1,
                    9.04,
                    9.4
                ],
                [
                    9.08,
                    9.34,
                    9.0,
                    9.6
                ],
                [
                    9.43,
                    9.03,
                    9.01,
                    9.43
                ],
                [
                    9.02,
                    9.04,
                    8.96,
                    9.09
                ],
                [
                    8.98,
                    8.75,
                    8.75,
                    9.01
                ],
                [
                    8.82,
                    8.83,
                    8.78,
                    9.2
                ],
                [
                    8.83,
                    8.84,
                    8.69,
                    8.88
                ],
                [
                    8.85,
                    8.92,
                    8.75,
                    8.95
                ],
                [
                    8.86,
                    8.74,
                    8.72,
                    8.88
                ],
                [
                    8.72,
                    8.52,
                    8.51,
                    8.72
                ],
                [
                    8.51,
                    8.46,
                    8.45,
                    8.62
                ],
                [
                    8.46,
                    8.54,
                    8.45,
                    8.55
                ],
                [
                    8.52,
                    8.42,
                    8.38,
                    8.53
                ],
                [
                    8.43,
                    8.77,
                    8.34,
                    8.85
                ],
                [
                    8.62,
                    8.73,
                    8.56,
                    8.8
                ],
                [
                    8.66,
                    8.64,
                    8.52,
                    8.7
                ],
                [
                    8.58,
                    8.73,
                    8.5,
                    8.78
                ],
                [
                    8.73,
                    9.06,
                    8.68,
                    9.06
                ],
                [
                    8.99,
                    8.88,
                    8.85,
                    9.06
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u7a81\u7834",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "pin",
            "symbolSize": 12,
            "data": [
                [
                    "2025-03-13",
                    8.35
                ],
                [
                    "2025-03-21",
                    10.38
                ],
                [
                    "2025-05-07",
                    9.42
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#1f77b4"
            }
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u6d3e\u53d1",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 12,
            "data": [
                [
                    "2025-01-03",
                    8.08
                ],
                [
                    "2025-01-17",
                    8.68
                ],
                [
                    "2025-03-07",
                    7.65
                ],
                [
                    "2025-03-17",
                    9.05
                ],
                [
                    "2025-03-24",
                    11.42
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#d62728"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u7a81\u7834",
                "\u5806\u91cf\u6d3e\u53d1"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-02",
                "2024-12-03",
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "8%",
            "right": "2%",
            "height": "85%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_d3fb99a13c3c49a2b5071f1f1715bd36.setOption(option_d3fb99a13c3c49a2b5071f1f1715bd36);
            window.addEventListener('resize', function(){
                chart_d3fb99a13c3c49a2b5071f1f1715bd36.resize();
            })
    </script>
</body>
</html>
