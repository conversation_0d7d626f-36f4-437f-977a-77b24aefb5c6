<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="ec073233475642febb18d3dfcf5d2819" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_ec073233475642febb18d3dfcf5d2819 = echarts.init(
            document.getElementById('ec073233475642febb18d3dfcf5d2819'), 'white', {renderer: 'canvas'});
        var option_ec073233475642febb18d3dfcf5d2819 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    14.0,
                    14.03,
                    13.84,
                    14.06
                ],
                [
                    14.03,
                    14.16,
                    13.9,
                    14.25
                ],
                [
                    14.18,
                    14.15,
                    14.06,
                    14.34
                ],
                [
                    14.4,
                    14.19,
                    14.17,
                    14.49
                ],
                [
                    14.18,
                    14.36,
                    14.12,
                    14.4
                ],
                [
                    14.32,
                    14.47,
                    14.27,
                    14.5
                ],
                [
                    14.41,
                    14.16,
                    14.15,
                    14.43
                ],
                [
                    14.2,
                    14.17,
                    14.12,
                    14.23
                ],
                [
                    14.16,
                    14.1,
                    14.02,
                    14.24
                ],
                [
                    14.12,
                    14.01,
                    13.98,
                    14.18
                ],
                [
                    13.95,
                    14.03,
                    13.79,
                    14.07
                ],
                [
                    14.02,
                    14.0,
                    13.95,
                    14.07
                ],
                [
                    14.0,
                    13.84,
                    13.82,
                    14.05
                ],
                [
                    13.84,
                    14.04,
                    13.84,
                    14.05
                ],
                [
                    14.08,
                    14.04,
                    13.93,
                    14.1
                ],
                [
                    14.04,
                    13.95,
                    13.93,
                    14.1
                ],
                [
                    13.94,
                    14.11,
                    13.92,
                    14.18
                ],
                [
                    14.06,
                    14.07,
                    14.01,
                    14.11
                ],
                [
                    14.05,
                    13.73,
                    13.71,
                    14.1
                ],
                [
                    13.78,
                    13.45,
                    13.38,
                    13.89
                ],
                [
                    13.5,
                    13.42,
                    13.37,
                    13.85
                ],
                [
                    13.45,
                    13.56,
                    13.31,
                    13.82
                ],
                [
                    13.56,
                    13.61,
                    13.44,
                    13.75
                ],
                [
                    13.56,
                    13.42,
                    13.18,
                    13.58
                ],
                [
                    13.31,
                    13.59,
                    13.31,
                    13.74
                ],
                [
                    13.69,
                    13.46,
                    13.46,
                    13.76
                ],
                [
                    13.52,
                    13.7,
                    13.42,
                    13.82
                ],
                [
                    13.64,
                    13.95,
                    13.59,
                    13.96
                ],
                [
                    13.96,
                    13.8,
                    13.75,
                    13.99
                ],
                [
                    13.85,
                    14.01,
                    13.85,
                    14.06
                ],
                [
                    14.19,
                    13.76,
                    13.74,
                    14.3
                ],
                [
                    13.78,
                    13.63,
                    13.61,
                    13.81
                ],
                [
                    13.66,
                    13.64,
                    13.5,
                    13.7
                ],
                [
                    13.6,
                    13.49,
                    13.45,
                    13.67
                ],
                [
                    13.55,
                    13.42,
                    13.42,
                    13.71
                ],
                [
                    13.42,
                    13.63,
                    13.38,
                    13.63
                ],
                [
                    13.68,
                    13.72,
                    13.57,
                    13.83
                ],
                [
                    13.86,
                    13.85,
                    13.73,
                    13.9
                ],
                [
                    13.82,
                    14.08,
                    13.81,
                    14.1
                ],
                [
                    14.02,
                    14.04,
                    13.95,
                    14.11
                ],
                [
                    13.99,
                    13.96,
                    13.93,
                    14.08
                ],
                [
                    14.01,
                    14.03,
                    13.98,
                    14.12
                ],
                [
                    13.99,
                    13.94,
                    13.83,
                    14.03
                ],
                [
                    13.92,
                    13.84,
                    13.84,
                    13.98
                ],
                [
                    13.84,
                    13.87,
                    13.75,
                    13.92
                ],
                [
                    13.87,
                    13.66,
                    13.62,
                    13.87
                ],
                [
                    13.66,
                    13.56,
                    13.51,
                    13.73
                ],
                [
                    13.57,
                    13.61,
                    13.56,
                    13.66
                ],
                [
                    13.62,
                    13.69,
                    13.59,
                    13.73
                ],
                [
                    13.7,
                    13.73,
                    13.66,
                    13.77
                ],
                [
                    13.72,
                    13.73,
                    13.56,
                    13.8
                ],
                [
                    13.69,
                    13.55,
                    13.52,
                    13.71
                ],
                [
                    13.55,
                    13.63,
                    13.53,
                    13.68
                ],
                [
                    13.64,
                    13.5,
                    13.39,
                    13.7
                ],
                [
                    13.47,
                    13.4,
                    13.36,
                    13.57
                ],
                [
                    13.45,
                    13.54,
                    13.41,
                    13.7
                ],
                [
                    13.51,
                    13.66,
                    13.45,
                    13.69
                ],
                [
                    13.6,
                    13.64,
                    13.47,
                    13.67
                ],
                [
                    13.67,
                    13.73,
                    13.63,
                    13.76
                ],
                [
                    13.66,
                    13.98,
                    13.66,
                    14.07
                ],
                [
                    14.06,
                    14.16,
                    14.05,
                    14.28
                ],
                [
                    14.16,
                    14.59,
                    14.07,
                    14.63
                ],
                [
                    14.53,
                    14.36,
                    14.33,
                    14.62
                ],
                [
                    14.39,
                    14.5,
                    14.26,
                    14.52
                ],
                [
                    14.75,
                    14.5,
                    14.44,
                    14.88
                ],
                [
                    14.5,
                    14.32,
                    14.28,
                    14.51
                ],
                [
                    14.32,
                    14.46,
                    14.24,
                    14.63
                ],
                [
                    14.48,
                    14.42,
                    14.35,
                    14.53
                ],
                [
                    14.42,
                    14.34,
                    14.29,
                    14.47
                ],
                [
                    14.3,
                    14.16,
                    14.09,
                    14.42
                ],
                [
                    14.17,
                    14.29,
                    14.06,
                    14.38
                ],
                [
                    14.23,
                    14.44,
                    14.2,
                    14.51
                ],
                [
                    14.48,
                    14.35,
                    14.29,
                    14.61
                ],
                [
                    14.28,
                    14.17,
                    14.05,
                    14.3
                ],
                [
                    14.22,
                    14.07,
                    13.95,
                    14.27
                ],
                [
                    14.02,
                    13.95,
                    13.79,
                    14.1
                ],
                [
                    13.95,
                    14.03,
                    13.92,
                    14.12
                ],
                [
                    14.04,
                    13.88,
                    13.84,
                    14.06
                ],
                [
                    13.85,
                    13.88,
                    13.73,
                    13.92
                ],
                [
                    13.14,
                    12.49,
                    12.49,
                    13.21
                ],
                [
                    12.54,
                    12.45,
                    12.27,
                    12.68
                ],
                [
                    12.31,
                    12.55,
                    11.91,
                    12.6
                ],
                [
                    12.72,
                    12.78,
                    12.69,
                    12.91
                ],
                [
                    12.78,
                    12.92,
                    12.71,
                    13.03
                ],
                [
                    12.98,
                    13.09,
                    12.98,
                    13.17
                ],
                [
                    13.07,
                    13.08,
                    12.91,
                    13.1
                ],
                [
                    13.08,
                    12.97,
                    12.8,
                    13.13
                ],
                [
                    12.97,
                    12.95,
                    12.92,
                    13.18
                ],
                [
                    12.9,
                    12.91,
                    12.79,
                    12.98
                ],
                [
                    12.92,
                    13.22,
                    12.87,
                    13.27
                ],
                [
                    13.25,
                    13.42,
                    13.14,
                    13.47
                ],
                [
                    13.28,
                    13.14,
                    13.14,
                    13.41
                ],
                [
                    13.15,
                    13.0,
                    12.97,
                    13.23
                ],
                [
                    13.28,
                    13.2,
                    13.18,
                    13.45
                ],
                [
                    13.25,
                    13.14,
                    13.08,
                    13.29
                ],
                [
                    13.17,
                    13.11,
                    13.06,
                    13.25
                ],
                [
                    13.17,
                    13.18,
                    13.16,
                    13.39
                ],
                [
                    13.22,
                    13.45,
                    13.22,
                    13.54
                ],
                [
                    13.49,
                    13.49,
                    13.38,
                    13.56
                ],
                [
                    13.44,
                    13.42,
                    13.38,
                    13.55
                ],
                [
                    13.42,
                    13.39,
                    13.27,
                    13.45
                ],
                [
                    13.4,
                    13.54,
                    13.37,
                    13.62
                ],
                [
                    13.62,
                    13.44,
                    13.4,
                    13.62
                ],
                [
                    13.45,
                    13.54,
                    13.36,
                    13.58
                ],
                [
                    13.54,
                    13.39,
                    13.38,
                    13.56
                ],
                [
                    13.4,
                    13.42,
                    13.39,
                    13.59
                ],
                [
                    13.42,
                    13.41,
                    13.29,
                    13.49
                ],
                [
                    13.4,
                    13.5,
                    13.38,
                    13.55
                ],
                [
                    13.73,
                    13.94,
                    13.73,
                    14.09
                ],
                [
                    13.97,
                    14.08,
                    13.88,
                    14.64
                ],
                [
                    14.29,
                    14.42,
                    14.25,
                    14.68
                ],
                [
                    14.6,
                    14.49,
                    14.35,
                    14.65
                ],
                [
                    14.44,
                    14.22,
                    14.13,
                    14.45
                ],
                [
                    14.17,
                    14.13,
                    14.04,
                    14.25
                ],
                [
                    14.09,
                    14.09,
                    14.02,
                    14.19
                ],
                [
                    14.08,
                    14.08,
                    14.0,
                    14.21
                ],
                [
                    14.11,
                    14.33,
                    14.08,
                    14.51
                ],
                [
                    14.35,
                    14.5,
                    14.33,
                    14.59
                ],
                [
                    14.6,
                    14.51,
                    14.48,
                    14.89
                ],
                [
                    14.98,
                    15.47,
                    14.91,
                    15.57
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "bar",
            "name": "\u6210\u4ea4\u91cf",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "legendHoverLink": true,
            "data": [
                67831.34,
                84959.75,
                76472.95,
                123133.88,
                89019.77,
                77481.55,
                89879.53,
                58319.84,
                63721.22,
                47498.36,
                60290.75,
                45830.65,
                48966.96,
                44449.26,
                49672.65,
                75566.34,
                65181.32,
                39215.67,
                71462.19,
                65690.27,
                70289.83,
                53573.39,
                38902.86,
                62319.8,
                60966.33,
                40204.93,
                66927.48,
                65417.99,
                47469.7,
                76002.94,
                173857.3,
                70868.45,
                44705.64,
                63313.82,
                72988.0,
                54221.11,
                78844.43,
                65344.84,
                82730.12,
                86058.35,
                60554.65,
                64659.14,
                59755.71,
                55386.93,
                61422.65,
                79235.99,
                65096.83,
                45626.84,
                60536.07,
                69515.63,
                73545.01,
                66888.91,
                55720.86,
                82839.3,
                62456.58,
                78358.1,
                48141.28,
                43129.91,
                72208.97,
                168044.98,
                138338.48,
                226768.94,
                125294.8,
                114079.75,
                166142.55,
                101567.35,
                103610.44,
                77549.38,
                72923.64,
                88331.25,
                104435.41,
                105636.81,
                123763.21,
                83173.8,
                79406.53,
                81328.03,
                63911.09,
                65891.22,
                66854.93,
                167341.32,
                137821.28,
                111576.18,
                104300.48,
                105016.47,
                86593.29,
                51800.49,
                60780.66,
                75439.96,
                53901.55,
                87626.63,
                95550.6,
                88961.23,
                57421.09,
                109562.68,
                69991.45,
                61133.81,
                99717.28,
                130225.59,
                113402.08,
                97688.23,
                73218.84,
                110171.69,
                87196.67,
                79034.57,
                54800.68,
                58312.24,
                43026.47,
                57175.15,
                210369.51,
                304678.43,
                279027.41,
                213834.76,
                159367.31,
                93428.76,
                96769.04,
                74869.94,
                157779.38,
                113643.63,
                144342.68,
                453181.38
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#7f7f7f"
            }
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 2,
            "yAxisIndex": 2,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    1.14
                ],
                [
                    "2024-12-06",
                    -8.11
                ],
                [
                    "2024-12-09",
                    6.39
                ],
                [
                    "2024-12-10",
                    -7.77
                ],
                [
                    "2024-12-11",
                    0.84
                ],
                [
                    "2024-12-12",
                    -0.94
                ],
                [
                    "2024-12-13",
                    3.98
                ],
                [
                    "2024-12-16",
                    10.37
                ],
                [
                    "2024-12-17",
                    4.4
                ],
                [
                    "2024-12-18",
                    -3.9
                ],
                [
                    "2024-12-19",
                    -4.21
                ],
                [
                    "2024-12-20",
                    4.1
                ],
                [
                    "2024-12-23",
                    7.33
                ],
                [
                    "2024-12-24",
                    12.31
                ],
                [
                    "2024-12-25",
                    -1.42
                ],
                [
                    "2024-12-26",
                    -19.92
                ],
                [
                    "2024-12-27",
                    -0.52
                ],
                [
                    "2024-12-30",
                    -7.58
                ],
                [
                    "2024-12-31",
                    -2.64
                ],
                [
                    "2025-01-02",
                    -6.05
                ],
                [
                    "2025-01-03",
                    -4.98
                ],
                [
                    "2025-01-06",
                    -2.94
                ],
                [
                    "2025-01-07",
                    5.71
                ],
                [
                    "2025-01-08",
                    0.18
                ],
                [
                    "2025-01-09",
                    11.48
                ],
                [
                    "2025-01-10",
                    4.48
                ],
                [
                    "2025-01-13",
                    8.78
                ],
                [
                    "2025-01-14",
                    4.74
                ],
                [
                    "2025-01-15",
                    -1.27
                ],
                [
                    "2025-01-16",
                    0.98
                ],
                [
                    "2025-01-17",
                    -9.4
                ],
                [
                    "2025-01-20",
                    -9.11
                ],
                [
                    "2025-01-21",
                    -10.94
                ],
                [
                    "2025-01-22",
                    -18.1
                ],
                [
                    "2025-01-23",
                    -5.89
                ],
                [
                    "2025-01-24",
                    -7.4
                ],
                [
                    "2025-01-27",
                    10.19
                ],
                [
                    "2025-02-05",
                    8.51
                ],
                [
                    "2025-02-06",
                    1.63
                ],
                [
                    "2025-02-07",
                    -2.72
                ],
                [
                    "2025-02-10",
                    -1.05
                ],
                [
                    "2025-02-11",
                    3.67
                ],
                [
                    "2025-02-12",
                    -16.18
                ],
                [
                    "2025-02-13",
                    -7.93
                ],
                [
                    "2025-02-14",
                    -6.99
                ],
                [
                    "2025-02-17",
                    -4.41
                ],
                [
                    "2025-02-18",
                    -3.01
                ],
                [
                    "2025-02-19",
                    -3.9
                ],
                [
                    "2025-02-20",
                    9.73
                ],
                [
                    "2025-02-21",
                    -0.52
                ],
                [
                    "2025-02-24",
                    -4.08
                ],
                [
                    "2025-02-25",
                    -15.68
                ],
                [
                    "2025-02-26",
                    -14.77
                ],
                [
                    "2025-02-27",
                    -7.11
                ],
                [
                    "2025-02-28",
                    -1.09
                ],
                [
                    "2025-03-03",
                    -4.52
                ],
                [
                    "2025-03-04",
                    7.24
                ],
                [
                    "2025-03-05",
                    -0.57
                ],
                [
                    "2025-03-06",
                    -0.8
                ],
                [
                    "2025-03-07",
                    -9.99
                ],
                [
                    "2025-03-10",
                    -1.84
                ],
                [
                    "2025-03-11",
                    2.64
                ],
                [
                    "2025-03-12",
                    -9.62
                ],
                [
                    "2025-03-13",
                    -1.15
                ],
                [
                    "2025-03-14",
                    -2.38
                ],
                [
                    "2025-03-17",
                    -7.37
                ],
                [
                    "2025-03-18",
                    7.31
                ],
                [
                    "2025-03-19",
                    -12.89
                ],
                [
                    "2025-03-20",
                    -10.59
                ],
                [
                    "2025-03-21",
                    -9.94
                ],
                [
                    "2025-03-24",
                    -1.78
                ],
                [
                    "2025-03-25",
                    2.31
                ],
                [
                    "2025-03-26",
                    1.6
                ],
                [
                    "2025-03-27",
                    -9.25
                ],
                [
                    "2025-03-28",
                    -11.83
                ],
                [
                    "2025-03-31",
                    -10.68
                ],
                [
                    "2025-04-01",
                    -2.29
                ],
                [
                    "2025-04-02",
                    -5.1
                ],
                [
                    "2025-04-03",
                    2.1
                ],
                [
                    "2025-04-07",
                    -14.12
                ],
                [
                    "2025-04-08",
                    -7.87
                ],
                [
                    "2025-04-09",
                    -2.08
                ],
                [
                    "2025-04-10",
                    4.91
                ],
                [
                    "2025-04-11",
                    -0.46
                ],
                [
                    "2025-04-14",
                    4.32
                ],
                [
                    "2025-04-15",
                    4.81
                ],
                [
                    "2025-04-16",
                    -10.03
                ],
                [
                    "2025-04-17",
                    1.76
                ],
                [
                    "2025-04-18",
                    -2.2
                ],
                [
                    "2025-04-21",
                    -1.89
                ],
                [
                    "2025-04-22",
                    4.21
                ],
                [
                    "2025-04-23",
                    -12.31
                ],
                [
                    "2025-04-24",
                    -12.56
                ],
                [
                    "2025-04-25",
                    -1.65
                ],
                [
                    "2025-04-28",
                    -11.81
                ],
                [
                    "2025-04-29",
                    7.1
                ],
                [
                    "2025-04-30",
                    -5.92
                ],
                [
                    "2025-05-06",
                    -17.85
                ],
                [
                    "2025-05-07",
                    6.99
                ],
                [
                    "2025-05-08",
                    -2.56
                ],
                [
                    "2025-05-09",
                    -0.56
                ],
                [
                    "2025-05-12",
                    -16.34
                ],
                [
                    "2025-05-13",
                    -10.2
                ],
                [
                    "2025-05-14",
                    -13.9
                ],
                [
                    "2025-05-15",
                    -3.24
                ],
                [
                    "2025-05-16",
                    -1.44
                ],
                [
                    "2025-05-19",
                    -4.96
                ],
                [
                    "2025-05-20",
                    -0.27
                ],
                [
                    "2025-05-21",
                    12.4
                ],
                [
                    "2025-05-22",
                    -1.22
                ],
                [
                    "2025-05-23",
                    -1.53
                ],
                [
                    "2025-05-26",
                    -6.95
                ],
                [
                    "2025-05-27",
                    -17.68
                ],
                [
                    "2025-05-28",
                    -18.6
                ],
                [
                    "2025-05-29",
                    10.85
                ],
                [
                    "2025-05-30",
                    12.61
                ],
                [
                    "2025-06-03",
                    0.72
                ],
                [
                    "2025-06-04",
                    -6.68
                ],
                [
                    "2025-06-05",
                    -3.46
                ],
                [
                    "2025-06-06",
                    3.26
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#5470C6"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u5165\u573a\u4fe1\u53f7",
            "xAxisIndex": 3,
            "yAxisIndex": 3,
            "symbol": "triangle",
            "symbolSize": 12,
            "data": [
                [
                    "2024-12-05",
                    null
                ],
                [
                    "2024-12-06",
                    null
                ],
                [
                    "2024-12-09",
                    null
                ],
                [
                    "2024-12-10",
                    null
                ],
                [
                    "2024-12-11",
                    null
                ],
                [
                    "2024-12-12",
                    null
                ],
                [
                    "2024-12-13",
                    14.15
                ],
                [
                    "2024-12-16",
                    14.12
                ],
                [
                    "2024-12-17",
                    null
                ],
                [
                    "2024-12-18",
                    null
                ],
                [
                    "2024-12-19",
                    null
                ],
                [
                    "2024-12-20",
                    13.95
                ],
                [
                    "2024-12-23",
                    13.82
                ],
                [
                    "2024-12-24",
                    null
                ],
                [
                    "2024-12-25",
                    null
                ],
                [
                    "2024-12-26",
                    null
                ],
                [
                    "2024-12-27",
                    null
                ],
                [
                    "2024-12-30",
                    null
                ],
                [
                    "2024-12-31",
                    null
                ],
                [
                    "2025-01-02",
                    null
                ],
                [
                    "2025-01-03",
                    null
                ],
                [
                    "2025-01-06",
                    null
                ],
                [
                    "2025-01-07",
                    null
                ],
                [
                    "2025-01-08",
                    null
                ],
                [
                    "2025-01-09",
                    13.31
                ],
                [
                    "2025-01-10",
                    13.46
                ],
                [
                    "2025-01-13",
                    13.42
                ],
                [
                    "2025-01-14",
                    null
                ],
                [
                    "2025-01-15",
                    null
                ],
                [
                    "2025-01-16",
                    null
                ],
                [
                    "2025-01-17",
                    null
                ],
                [
                    "2025-01-20",
                    null
                ],
                [
                    "2025-01-21",
                    null
                ],
                [
                    "2025-01-22",
                    null
                ],
                [
                    "2025-01-23",
                    null
                ],
                [
                    "2025-01-24",
                    null
                ],
                [
                    "2025-01-27",
                    13.57
                ],
                [
                    "2025-02-05",
                    null
                ],
                [
                    "2025-02-06",
                    null
                ],
                [
                    "2025-02-07",
                    null
                ],
                [
                    "2025-02-10",
                    null
                ],
                [
                    "2025-02-11",
                    null
                ],
                [
                    "2025-02-12",
                    null
                ],
                [
                    "2025-02-13",
                    null
                ],
                [
                    "2025-02-14",
                    null
                ],
                [
                    "2025-02-17",
                    null
                ],
                [
                    "2025-02-18",
                    null
                ],
                [
                    "2025-02-19",
                    null
                ],
                [
                    "2025-02-20",
                    null
                ],
                [
                    "2025-02-21",
                    null
                ],
                [
                    "2025-02-24",
                    null
                ],
                [
                    "2025-02-25",
                    null
                ],
                [
                    "2025-02-26",
                    null
                ],
                [
                    "2025-02-27",
                    null
                ],
                [
                    "2025-02-28",
                    null
                ],
                [
                    "2025-03-03",
                    null
                ],
                [
                    "2025-03-04",
                    null
                ],
                [
                    "2025-03-05",
                    null
                ],
                [
                    "2025-03-06",
                    null
                ],
                [
                    "2025-03-07",
                    null
                ],
                [
                    "2025-03-10",
                    null
                ],
                [
                    "2025-03-11",
                    null
                ],
                [
                    "2025-03-12",
                    null
                ],
                [
                    "2025-03-13",
                    null
                ],
                [
                    "2025-03-14",
                    null
                ],
                [
                    "2025-03-17",
                    null
                ],
                [
                    "2025-03-18",
                    null
                ],
                [
                    "2025-03-19",
                    null
                ],
                [
                    "2025-03-20",
                    null
                ],
                [
                    "2025-03-21",
                    null
                ],
                [
                    "2025-03-24",
                    null
                ],
                [
                    "2025-03-25",
                    null
                ],
                [
                    "2025-03-26",
                    null
                ],
                [
                    "2025-03-27",
                    null
                ],
                [
                    "2025-03-28",
                    null
                ],
                [
                    "2025-03-31",
                    null
                ],
                [
                    "2025-04-01",
                    null
                ],
                [
                    "2025-04-02",
                    null
                ],
                [
                    "2025-04-03",
                    null
                ],
                [
                    "2025-04-07",
                    null
                ],
                [
                    "2025-04-08",
                    null
                ],
                [
                    "2025-04-09",
                    null
                ],
                [
                    "2025-04-10",
                    null
                ],
                [
                    "2025-04-11",
                    null
                ],
                [
                    "2025-04-14",
                    12.98
                ],
                [
                    "2025-04-15",
                    null
                ],
                [
                    "2025-04-16",
                    null
                ],
                [
                    "2025-04-17",
                    null
                ],
                [
                    "2025-04-18",
                    null
                ],
                [
                    "2025-04-21",
                    null
                ],
                [
                    "2025-04-22",
                    null
                ],
                [
                    "2025-04-23",
                    null
                ],
                [
                    "2025-04-24",
                    null
                ],
                [
                    "2025-04-25",
                    null
                ],
                [
                    "2025-04-28",
                    null
                ],
                [
                    "2025-04-29",
                    null
                ],
                [
                    "2025-04-30",
                    null
                ],
                [
                    "2025-05-06",
                    null
                ],
                [
                    "2025-05-07",
                    null
                ],
                [
                    "2025-05-08",
                    null
                ],
                [
                    "2025-05-09",
                    null
                ],
                [
                    "2025-05-12",
                    null
                ],
                [
                    "2025-05-13",
                    null
                ],
                [
                    "2025-05-14",
                    null
                ],
                [
                    "2025-05-15",
                    null
                ],
                [
                    "2025-05-16",
                    null
                ],
                [
                    "2025-05-19",
                    null
                ],
                [
                    "2025-05-20",
                    null
                ],
                [
                    "2025-05-21",
                    null
                ],
                [
                    "2025-05-22",
                    null
                ],
                [
                    "2025-05-23",
                    null
                ],
                [
                    "2025-05-26",
                    null
                ],
                [
                    "2025-05-27",
                    null
                ],
                [
                    "2025-05-28",
                    null
                ],
                [
                    "2025-05-29",
                    14.02
                ],
                [
                    "2025-05-30",
                    null
                ],
                [
                    "2025-06-03",
                    null
                ],
                [
                    "2025-06-04",
                    null
                ],
                [
                    "2025-06-05",
                    null
                ],
                [
                    "2025-06-06",
                    null
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "purple"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u6210\u4ea4\u91cf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u5165\u573a\u4fe1\u53f7"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 2,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "position": "right",
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600459 \u4e3b\u529b\u5165\u573a\u4fe1\u53f7 K \u7ebf\u56fe",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 50,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 50,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 50,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 50,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "5%",
            "right": "8%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "50%",
            "right": "8%",
            "height": "15%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "70%",
            "right": "8%",
            "height": "15%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "5%",
            "right": "8%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_ec073233475642febb18d3dfcf5d2819.setOption(option_ec073233475642febb18d3dfcf5d2819);
            window.addEventListener('resize', function(){
                chart_ec073233475642febb18d3dfcf5d2819.resize();
            })
    </script>
</body>
</html>
