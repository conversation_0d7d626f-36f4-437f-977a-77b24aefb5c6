<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="880aa8f0a91349f485b6af755fc8e624" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_880aa8f0a91349f485b6af755fc8e624 = echarts.init(
            document.getElementById('880aa8f0a91349f485b6af755fc8e624'), 'white', {renderer: 'canvas'});
            
    // 等待页面加载完成
    setTimeout(function() {
        // 获取所有图表实例
        var charts = [];
        var chartDoms = document.querySelectorAll('[_echarts_instance_]');
        chartDoms.forEach(function(dom) {
            var chart = echarts.getInstanceByDom(dom);
            if (chart) {
                charts.push(chart);
            }
        });

        console.log('找到图表数量:', charts.length);

        // 设置缩放联动
        if (charts.length >= 2) {
            echarts.connect(charts);
            console.log('图表缩放联动已设置');
        }
    }, 100);

        var option_880aa8f0a91349f485b6af755fc8e624 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    12.85,
                    13.1,
                    12.61,
                    13.15
                ],
                [
                    13.1,
                    13.02,
                    12.65,
                    13.19
                ],
                [
                    12.99,
                    12.67,
                    12.61,
                    13.05
                ],
                [
                    12.6,
                    12.79,
                    12.58,
                    12.88
                ],
                [
                    12.86,
                    12.91,
                    12.67,
                    13.03
                ],
                [
                    12.92,
                    13.19,
                    12.92,
                    13.82
                ],
                [
                    13.98,
                    13.23,
                    13.18,
                    14.01
                ],
                [
                    13.19,
                    13.16,
                    13.06,
                    13.28
                ],
                [
                    13.12,
                    13.32,
                    12.91,
                    13.32
                ],
                [
                    13.2,
                    13.69,
                    13.08,
                    14.08
                ],
                [
                    13.71,
                    12.67,
                    12.58,
                    13.84
                ],
                [
                    12.62,
                    12.45,
                    12.4,
                    12.75
                ],
                [
                    12.56,
                    12.61,
                    12.37,
                    12.79
                ],
                [
                    12.44,
                    12.72,
                    12.36,
                    12.77
                ],
                [
                    12.63,
                    12.73,
                    12.58,
                    12.88
                ],
                [
                    12.66,
                    12.27,
                    12.24,
                    12.73
                ],
                [
                    12.29,
                    12.31,
                    12.15,
                    12.39
                ],
                [
                    12.3,
                    12.05,
                    11.95,
                    12.31
                ],
                [
                    12.05,
                    12.19,
                    12.02,
                    12.3
                ],
                [
                    12.22,
                    12.22,
                    12.16,
                    12.57
                ],
                [
                    12.18,
                    12.26,
                    12.04,
                    12.3
                ],
                [
                    12.3,
                    11.76,
                    11.76,
                    12.36
                ],
                [
                    11.75,
                    11.22,
                    11.13,
                    11.8
                ],
                [
                    11.22,
                    10.72,
                    10.72,
                    11.3
                ],
                [
                    10.4,
                    10.69,
                    10.4,
                    10.88
                ],
                [
                    10.73,
                    11.02,
                    10.69,
                    11.02
                ],
                [
                    10.97,
                    10.72,
                    10.48,
                    10.97
                ],
                [
                    10.65,
                    10.68,
                    10.6,
                    10.8
                ],
                [
                    10.68,
                    10.37,
                    10.36,
                    10.87
                ],
                [
                    10.34,
                    10.64,
                    10.26,
                    10.68
                ],
                [
                    10.69,
                    11.0,
                    10.59,
                    11.04
                ],
                [
                    11.0,
                    10.87,
                    10.81,
                    11.03
                ],
                [
                    10.95,
                    10.87,
                    10.76,
                    11.17
                ],
                [
                    10.8,
                    10.92,
                    10.77,
                    11.0
                ],
                [
                    11.0,
                    10.98,
                    10.91,
                    11.09
                ],
                [
                    11.02,
                    11.02,
                    10.79,
                    11.11
                ],
                [
                    10.96,
                    10.89,
                    10.81,
                    10.98
                ],
                [
                    11.21,
                    11.38,
                    11.13,
                    11.71
                ],
                [
                    11.33,
                    11.52,
                    11.27,
                    11.72
                ],
                [
                    11.67,
                    11.68,
                    11.53,
                    11.88
                ],
                [
                    12.04,
                    12.22,
                    11.9,
                    12.42
                ],
                [
                    12.1,
                    12.28,
                    11.95,
                    12.28
                ],
                [
                    12.19,
                    12.72,
                    12.19,
                    12.85
                ],
                [
                    12.84,
                    13.17,
                    12.79,
                    13.26
                ],
                [
                    13.15,
                    13.03,
                    12.9,
                    13.22
                ],
                [
                    13.05,
                    13.45,
                    13.02,
                    13.5
                ],
                [
                    13.63,
                    13.26,
                    13.16,
                    13.63
                ],
                [
                    13.25,
                    13.99,
                    13.22,
                    14.19
                ],
                [
                    14.35,
                    13.95,
                    13.85,
                    14.66
                ],
                [
                    13.89,
                    13.39,
                    13.32,
                    14.23
                ],
                [
                    13.46,
                    14.06,
                    13.39,
                    14.11
                ],
                [
                    13.98,
                    13.91,
                    13.85,
                    14.34
                ],
                [
                    13.98,
                    14.24,
                    13.9,
                    14.28
                ],
                [
                    14.11,
                    13.9,
                    13.78,
                    14.24
                ],
                [
                    13.68,
                    14.1,
                    13.5,
                    14.3
                ],
                [
                    14.1,
                    14.0,
                    13.9,
                    14.25
                ],
                [
                    13.98,
                    13.72,
                    13.5,
                    14.08
                ],
                [
                    13.69,
                    13.16,
                    13.01,
                    13.75
                ],
                [
                    13.12,
                    13.26,
                    12.93,
                    13.51
                ],
                [
                    13.08,
                    13.6,
                    13.06,
                    13.76
                ],
                [
                    13.7,
                    13.82,
                    13.63,
                    14.04
                ],
                [
                    13.9,
                    15.2,
                    13.84,
                    15.2
                ],
                [
                    15.5,
                    15.05,
                    14.83,
                    15.53
                ],
                [
                    15.05,
                    14.8,
                    14.59,
                    15.09
                ],
                [
                    14.5,
                    14.63,
                    14.36,
                    14.9
                ],
                [
                    14.75,
                    14.42,
                    14.39,
                    14.77
                ],
                [
                    14.49,
                    14.21,
                    14.07,
                    14.52
                ],
                [
                    14.15,
                    14.43,
                    13.91,
                    14.48
                ],
                [
                    14.62,
                    15.0,
                    14.4,
                    15.09
                ],
                [
                    15.0,
                    15.0,
                    14.82,
                    15.33
                ],
                [
                    14.89,
                    15.04,
                    14.78,
                    15.2
                ],
                [
                    14.97,
                    14.67,
                    14.65,
                    15.01
                ],
                [
                    14.55,
                    14.34,
                    14.21,
                    14.62
                ],
                [
                    14.32,
                    13.53,
                    13.31,
                    14.32
                ],
                [
                    13.8,
                    14.02,
                    13.75,
                    14.27
                ],
                [
                    13.97,
                    14.11,
                    13.85,
                    14.25
                ],
                [
                    14.02,
                    14.11,
                    13.77,
                    14.24
                ],
                [
                    14.11,
                    14.03,
                    13.97,
                    14.23
                ],
                [
                    13.91,
                    14.49,
                    13.8,
                    14.58
                ],
                [
                    14.44,
                    14.49,
                    14.32,
                    14.77
                ],
                [
                    14.45,
                    14.62,
                    14.4,
                    15.02
                ],
                [
                    14.47,
                    14.7,
                    14.4,
                    14.99
                ],
                [
                    13.7,
                    13.26,
                    13.23,
                    14.49
                ],
                [
                    13.59,
                    13.86,
                    13.56,
                    14.1
                ],
                [
                    13.72,
                    14.51,
                    13.23,
                    14.6
                ],
                [
                    14.65,
                    14.69,
                    14.65,
                    15.05
                ],
                [
                    14.55,
                    14.6,
                    14.54,
                    14.84
                ],
                [
                    14.77,
                    14.65,
                    14.57,
                    14.96
                ],
                [
                    14.59,
                    14.25,
                    13.99,
                    14.64
                ],
                [
                    14.19,
                    14.37,
                    14.12,
                    14.8
                ],
                [
                    14.18,
                    15.81,
                    14.12,
                    15.81
                ],
                [
                    16.03,
                    15.43,
                    15.4,
                    16.2
                ],
                [
                    15.51,
                    15.78,
                    15.22,
                    15.93
                ],
                [
                    15.75,
                    15.55,
                    15.46,
                    15.89
                ],
                [
                    15.65,
                    15.15,
                    15.1,
                    15.78
                ],
                [
                    15.1,
                    14.98,
                    14.86,
                    15.26
                ],
                [
                    14.96,
                    14.8,
                    14.77,
                    15.35
                ],
                [
                    14.75,
                    14.37,
                    14.33,
                    15.05
                ],
                [
                    14.55,
                    15.05,
                    14.46,
                    15.25
                ],
                [
                    15.01,
                    14.75,
                    14.74,
                    15.18
                ],
                [
                    14.78,
                    15.16,
                    14.78,
                    15.22
                ],
                [
                    15.31,
                    15.11,
                    14.97,
                    15.49
                ],
                [
                    15.05,
                    15.07,
                    15.01,
                    15.25
                ],
                [
                    15.0,
                    14.63,
                    14.6,
                    15.01
                ],
                [
                    14.75,
                    14.8,
                    14.66,
                    14.88
                ],
                [
                    14.95,
                    14.66,
                    14.55,
                    14.97
                ],
                [
                    14.6,
                    14.73,
                    14.53,
                    14.85
                ],
                [
                    14.65,
                    14.06,
                    14.01,
                    14.66
                ],
                [
                    14.02,
                    13.97,
                    13.91,
                    14.12
                ],
                [
                    13.98,
                    14.21,
                    13.83,
                    14.25
                ],
                [
                    14.21,
                    14.14,
                    14.09,
                    14.24
                ],
                [
                    14.15,
                    14.06,
                    14.01,
                    14.18
                ],
                [
                    14.0,
                    13.9,
                    13.88,
                    14.16
                ],
                [
                    13.89,
                    13.88,
                    13.86,
                    14.23
                ],
                [
                    13.91,
                    13.91,
                    13.78,
                    14.03
                ],
                [
                    13.93,
                    13.82,
                    13.75,
                    13.95
                ],
                [
                    13.86,
                    13.66,
                    13.66,
                    13.93
                ],
                [
                    13.57,
                    14.01,
                    13.49,
                    14.05
                ],
                [
                    14.0,
                    13.67,
                    13.62,
                    14.0
                ],
                [
                    13.8,
                    14.17,
                    13.75,
                    14.37
                ],
                [
                    14.2,
                    14.3,
                    14.13,
                    14.32
                ],
                [
                    14.3,
                    14.45,
                    14.22,
                    14.49
                ],
                [
                    14.45,
                    14.42,
                    14.32,
                    14.55
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    -2.16
                ],
                [
                    "2024-12-06",
                    3.79
                ],
                [
                    "2024-12-09",
                    7.09
                ],
                [
                    "2024-12-10",
                    -2.21
                ],
                [
                    "2024-12-11",
                    -11.64
                ],
                [
                    "2024-12-12",
                    -2.65
                ],
                [
                    "2024-12-13",
                    4.74
                ],
                [
                    "2024-12-16",
                    -12.64
                ],
                [
                    "2024-12-17",
                    -7.05
                ],
                [
                    "2024-12-18",
                    1.83
                ],
                [
                    "2024-12-19",
                    -5.07
                ],
                [
                    "2024-12-20",
                    -0.07
                ],
                [
                    "2024-12-23",
                    -9.92
                ],
                [
                    "2024-12-24",
                    -8.16
                ],
                [
                    "2024-12-25",
                    -29.76
                ],
                [
                    "2024-12-26",
                    -0.34
                ],
                [
                    "2024-12-27",
                    -0.44
                ],
                [
                    "2024-12-30",
                    -0.36
                ],
                [
                    "2024-12-31",
                    -1.87
                ],
                [
                    "2025-01-02",
                    2.2
                ],
                [
                    "2025-01-03",
                    -3.7
                ],
                [
                    "2025-01-06",
                    -5.82
                ],
                [
                    "2025-01-07",
                    -0.94
                ],
                [
                    "2025-01-08",
                    -6.54
                ],
                [
                    "2025-01-09",
                    0.5
                ],
                [
                    "2025-01-10",
                    -2.53
                ],
                [
                    "2025-01-13",
                    -6.27
                ],
                [
                    "2025-01-14",
                    0.53
                ],
                [
                    "2025-01-15",
                    -3.04
                ],
                [
                    "2025-01-16",
                    1.63
                ],
                [
                    "2025-01-17",
                    2.79
                ],
                [
                    "2025-01-20",
                    -8.15
                ],
                [
                    "2025-01-21",
                    5.44
                ],
                [
                    "2025-01-22",
                    -8.53
                ],
                [
                    "2025-01-23",
                    4.31
                ],
                [
                    "2025-01-24",
                    1.19
                ],
                [
                    "2025-01-27",
                    7.46
                ],
                [
                    "2025-02-05",
                    14.07
                ],
                [
                    "2025-02-06",
                    0.96
                ],
                [
                    "2025-02-07",
                    1.62
                ],
                [
                    "2025-02-10",
                    6.0
                ],
                [
                    "2025-02-11",
                    -6.7
                ],
                [
                    "2025-02-12",
                    0.49
                ],
                [
                    "2025-02-13",
                    -4.22
                ],
                [
                    "2025-02-14",
                    2.12
                ],
                [
                    "2025-02-17",
                    -12.33
                ],
                [
                    "2025-02-18",
                    -11.94
                ],
                [
                    "2025-02-19",
                    0.31
                ],
                [
                    "2025-02-20",
                    -5.63
                ],
                [
                    "2025-02-21",
                    -2.6
                ],
                [
                    "2025-02-24",
                    -9.8
                ],
                [
                    "2025-02-25",
                    -9.96
                ],
                [
                    "2025-02-26",
                    -13.69
                ],
                [
                    "2025-02-27",
                    -14.68
                ],
                [
                    "2025-02-28",
                    -8.35
                ],
                [
                    "2025-03-03",
                    -1.89
                ],
                [
                    "2025-03-04",
                    -2.87
                ],
                [
                    "2025-03-05",
                    -3.37
                ],
                [
                    "2025-03-06",
                    18.47
                ],
                [
                    "2025-03-07",
                    -5.49
                ],
                [
                    "2025-03-10",
                    -1.4
                ],
                [
                    "2025-03-11",
                    -3.59
                ],
                [
                    "2025-03-12",
                    -12.59
                ],
                [
                    "2025-03-13",
                    -9.91
                ],
                [
                    "2025-03-14",
                    -4.0
                ],
                [
                    "2025-03-17",
                    4.8
                ],
                [
                    "2025-03-18",
                    -2.64
                ],
                [
                    "2025-03-19",
                    8.5
                ],
                [
                    "2025-03-20",
                    -7.07
                ],
                [
                    "2025-03-21",
                    -9.18
                ],
                [
                    "2025-03-24",
                    -9.52
                ],
                [
                    "2025-03-25",
                    4.2
                ],
                [
                    "2025-03-26",
                    0.78
                ],
                [
                    "2025-03-27",
                    3.17
                ],
                [
                    "2025-03-28",
                    -11.49
                ],
                [
                    "2025-03-31",
                    11.13
                ],
                [
                    "2025-04-01",
                    4.46
                ],
                [
                    "2025-04-02",
                    6.52
                ],
                [
                    "2025-04-03",
                    6.66
                ],
                [
                    "2025-04-07",
                    -10.88
                ],
                [
                    "2025-04-08",
                    -1.07
                ],
                [
                    "2025-04-09",
                    8.35
                ],
                [
                    "2025-04-10",
                    1.14
                ],
                [
                    "2025-04-11",
                    3.75
                ],
                [
                    "2025-04-14",
                    3.76
                ],
                [
                    "2025-04-15",
                    -9.36
                ],
                [
                    "2025-04-16",
                    1.99
                ],
                [
                    "2025-04-17",
                    27.91
                ],
                [
                    "2025-04-18",
                    -7.41
                ],
                [
                    "2025-04-21",
                    0.07
                ],
                [
                    "2025-04-22",
                    -1.68
                ],
                [
                    "2025-04-23",
                    -9.59
                ],
                [
                    "2025-04-24",
                    -4.82
                ],
                [
                    "2025-04-25",
                    -7.1
                ],
                [
                    "2025-04-28",
                    -9.57
                ],
                [
                    "2025-04-29",
                    9.47
                ],
                [
                    "2025-04-30",
                    -6.55
                ],
                [
                    "2025-05-06",
                    -0.27
                ],
                [
                    "2025-05-07",
                    1.91
                ],
                [
                    "2025-05-08",
                    -4.94
                ],
                [
                    "2025-05-09",
                    -18.59
                ],
                [
                    "2025-05-12",
                    -0.24
                ],
                [
                    "2025-05-13",
                    -11.36
                ],
                [
                    "2025-05-14",
                    1.63
                ],
                [
                    "2025-05-15",
                    -14.89
                ],
                [
                    "2025-05-16",
                    -7.17
                ],
                [
                    "2025-05-19",
                    5.61
                ],
                [
                    "2025-05-20",
                    -3.32
                ],
                [
                    "2025-05-21",
                    -8.33
                ],
                [
                    "2025-05-22",
                    -15.81
                ],
                [
                    "2025-05-23",
                    -7.93
                ],
                [
                    "2025-05-26",
                    -1.31
                ],
                [
                    "2025-05-27",
                    -4.59
                ],
                [
                    "2025-05-28",
                    -11.9
                ],
                [
                    "2025-05-29",
                    4.68
                ],
                [
                    "2025-05-30",
                    -1.73
                ],
                [
                    "2025-06-03",
                    -0.35
                ],
                [
                    "2025-06-04",
                    4.66
                ],
                [
                    "2025-06-05",
                    3.13
                ],
                [
                    "2025-06-06",
                    -9.88
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    0.86
                ],
                [
                    "2024-12-06",
                    -0.01
                ],
                [
                    "2024-12-09",
                    0.29
                ],
                [
                    "2024-12-10",
                    1.06
                ],
                [
                    "2024-12-11",
                    -0.09
                ],
                [
                    "2024-12-12",
                    -2.42
                ],
                [
                    "2024-12-13",
                    -1.83
                ],
                [
                    "2024-12-16",
                    8.04
                ],
                [
                    "2024-12-17",
                    -6.43
                ],
                [
                    "2024-12-18",
                    -6.99
                ],
                [
                    "2024-12-19",
                    -2.8
                ],
                [
                    "2024-12-20",
                    -4.92
                ],
                [
                    "2024-12-23",
                    1.9
                ],
                [
                    "2024-12-24",
                    1.25
                ],
                [
                    "2024-12-25",
                    6.79
                ],
                [
                    "2024-12-26",
                    -8.65
                ],
                [
                    "2024-12-27",
                    2.92
                ],
                [
                    "2024-12-30",
                    -2.49
                ],
                [
                    "2024-12-31",
                    3.9
                ],
                [
                    "2025-01-02",
                    1.47
                ],
                [
                    "2025-01-03",
                    -3.33
                ],
                [
                    "2025-01-06",
                    -9.17
                ],
                [
                    "2025-01-07",
                    0.65
                ],
                [
                    "2025-01-08",
                    3.24
                ],
                [
                    "2025-01-09",
                    2.4
                ],
                [
                    "2025-01-10",
                    -5.76
                ],
                [
                    "2025-01-13",
                    -3.38
                ],
                [
                    "2025-01-14",
                    2.39
                ],
                [
                    "2025-01-15",
                    2.05
                ],
                [
                    "2025-01-16",
                    -2.0
                ],
                [
                    "2025-01-17",
                    -6.51
                ],
                [
                    "2025-01-20",
                    2.93
                ],
                [
                    "2025-01-21",
                    -0.19
                ],
                [
                    "2025-01-22",
                    -3.29
                ],
                [
                    "2025-01-23",
                    3.27
                ],
                [
                    "2025-01-24",
                    10.5
                ],
                [
                    "2025-01-27",
                    3.51
                ],
                [
                    "2025-02-05",
                    -7.16
                ],
                [
                    "2025-02-06",
                    -6.56
                ],
                [
                    "2025-02-07",
                    -4.45
                ],
                [
                    "2025-02-10",
                    -0.61
                ],
                [
                    "2025-02-11",
                    3.45
                ],
                [
                    "2025-02-12",
                    -2.66
                ],
                [
                    "2025-02-13",
                    -1.97
                ],
                [
                    "2025-02-14",
                    -2.02
                ],
                [
                    "2025-02-17",
                    4.98
                ],
                [
                    "2025-02-18",
                    -5.03
                ],
                [
                    "2025-02-19",
                    4.47
                ],
                [
                    "2025-02-20",
                    8.71
                ],
                [
                    "2025-02-21",
                    -4.23
                ],
                [
                    "2025-02-24",
                    -4.94
                ],
                [
                    "2025-02-25",
                    4.1
                ],
                [
                    "2025-02-26",
                    3.73
                ],
                [
                    "2025-02-27",
                    -3.01
                ],
                [
                    "2025-02-28",
                    -0.89
                ],
                [
                    "2025-03-03",
                    6.07
                ],
                [
                    "2025-03-04",
                    5.58
                ],
                [
                    "2025-03-05",
                    1.41
                ],
                [
                    "2025-03-06",
                    -6.03
                ],
                [
                    "2025-03-07",
                    3.03
                ],
                [
                    "2025-03-10",
                    -2.57
                ],
                [
                    "2025-03-11",
                    -0.49
                ],
                [
                    "2025-03-12",
                    6.25
                ],
                [
                    "2025-03-13",
                    3.06
                ],
                [
                    "2025-03-14",
                    0.23
                ],
                [
                    "2025-03-17",
                    4.72
                ],
                [
                    "2025-03-18",
                    4.44
                ],
                [
                    "2025-03-19",
                    2.2
                ],
                [
                    "2025-03-20",
                    1.54
                ],
                [
                    "2025-03-21",
                    -3.2
                ],
                [
                    "2025-03-24",
                    1.99
                ],
                [
                    "2025-03-25",
                    -4.66
                ],
                [
                    "2025-03-26",
                    2.98
                ],
                [
                    "2025-03-27",
                    -2.37
                ],
                [
                    "2025-03-28",
                    3.48
                ],
                [
                    "2025-03-31",
                    -2.63
                ],
                [
                    "2025-04-01",
                    3.35
                ],
                [
                    "2025-04-02",
                    -0.09
                ],
                [
                    "2025-04-03",
                    1.95
                ],
                [
                    "2025-04-07",
                    0.1
                ],
                [
                    "2025-04-08",
                    1.11
                ],
                [
                    "2025-04-09",
                    2.88
                ],
                [
                    "2025-04-10",
                    2.53
                ],
                [
                    "2025-04-11",
                    1.61
                ],
                [
                    "2025-04-14",
                    3.67
                ],
                [
                    "2025-04-15",
                    -2.42
                ],
                [
                    "2025-04-16",
                    1.39
                ],
                [
                    "2025-04-17",
                    -12.2
                ],
                [
                    "2025-04-18",
                    -0.97
                ],
                [
                    "2025-04-21",
                    2.5
                ],
                [
                    "2025-04-22",
                    2.01
                ],
                [
                    "2025-04-23",
                    1.7
                ],
                [
                    "2025-04-24",
                    -2.82
                ],
                [
                    "2025-04-25",
                    4.74
                ],
                [
                    "2025-04-28",
                    -5.22
                ],
                [
                    "2025-04-29",
                    -2.7
                ],
                [
                    "2025-04-30",
                    6.1
                ],
                [
                    "2025-05-06",
                    -1.58
                ],
                [
                    "2025-05-07",
                    0.54
                ],
                [
                    "2025-05-08",
                    0.31
                ],
                [
                    "2025-05-09",
                    6.7
                ],
                [
                    "2025-05-12",
                    -2.92
                ],
                [
                    "2025-05-13",
                    5.0
                ],
                [
                    "2025-05-14",
                    -1.73
                ],
                [
                    "2025-05-15",
                    1.58
                ],
                [
                    "2025-05-16",
                    0.18
                ],
                [
                    "2025-05-19",
                    -1.26
                ],
                [
                    "2025-05-20",
                    -1.79
                ],
                [
                    "2025-05-21",
                    3.63
                ],
                [
                    "2025-05-22",
                    -1.92
                ],
                [
                    "2025-05-23",
                    -1.07
                ],
                [
                    "2025-05-26",
                    0.36
                ],
                [
                    "2025-05-27",
                    -0.91
                ],
                [
                    "2025-05-28",
                    1.18
                ],
                [
                    "2025-05-29",
                    -7.97
                ],
                [
                    "2025-05-30",
                    -3.55
                ],
                [
                    "2025-06-03",
                    -2.72
                ],
                [
                    "2025-06-04",
                    0.07
                ],
                [
                    "2025-06-05",
                    1.51
                ],
                [
                    "2025-06-06",
                    6.78
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    1.3
                ],
                [
                    "2024-12-06",
                    -3.78
                ],
                [
                    "2024-12-09",
                    -7.38
                ],
                [
                    "2024-12-10",
                    1.14
                ],
                [
                    "2024-12-11",
                    11.72
                ],
                [
                    "2024-12-12",
                    5.08
                ],
                [
                    "2024-12-13",
                    -2.91
                ],
                [
                    "2024-12-16",
                    4.6
                ],
                [
                    "2024-12-17",
                    13.48
                ],
                [
                    "2024-12-18",
                    5.16
                ],
                [
                    "2024-12-19",
                    7.88
                ],
                [
                    "2024-12-20",
                    4.98
                ],
                [
                    "2024-12-23",
                    8.02
                ],
                [
                    "2024-12-24",
                    6.91
                ],
                [
                    "2024-12-25",
                    22.97
                ],
                [
                    "2024-12-26",
                    8.99
                ],
                [
                    "2024-12-27",
                    -2.49
                ],
                [
                    "2024-12-30",
                    2.85
                ],
                [
                    "2024-12-31",
                    -2.03
                ],
                [
                    "2025-01-02",
                    -3.67
                ],
                [
                    "2025-01-03",
                    7.03
                ],
                [
                    "2025-01-06",
                    15.0
                ],
                [
                    "2025-01-07",
                    0.28
                ],
                [
                    "2025-01-08",
                    3.29
                ],
                [
                    "2025-01-09",
                    -2.9
                ],
                [
                    "2025-01-10",
                    8.29
                ],
                [
                    "2025-01-13",
                    9.65
                ],
                [
                    "2025-01-14",
                    -2.92
                ],
                [
                    "2025-01-15",
                    0.99
                ],
                [
                    "2025-01-16",
                    0.37
                ],
                [
                    "2025-01-17",
                    3.72
                ],
                [
                    "2025-01-20",
                    5.22
                ],
                [
                    "2025-01-21",
                    -5.25
                ],
                [
                    "2025-01-22",
                    11.82
                ],
                [
                    "2025-01-23",
                    -7.58
                ],
                [
                    "2025-01-24",
                    -11.69
                ],
                [
                    "2025-01-27",
                    -10.97
                ],
                [
                    "2025-02-05",
                    -6.91
                ],
                [
                    "2025-02-06",
                    5.61
                ],
                [
                    "2025-02-07",
                    2.83
                ],
                [
                    "2025-02-10",
                    -5.39
                ],
                [
                    "2025-02-11",
                    3.25
                ],
                [
                    "2025-02-12",
                    2.17
                ],
                [
                    "2025-02-13",
                    6.18
                ],
                [
                    "2025-02-14",
                    -0.1
                ],
                [
                    "2025-02-17",
                    7.35
                ],
                [
                    "2025-02-18",
                    16.97
                ],
                [
                    "2025-02-19",
                    -4.78
                ],
                [
                    "2025-02-20",
                    -3.08
                ],
                [
                    "2025-02-21",
                    6.83
                ],
                [
                    "2025-02-24",
                    14.74
                ],
                [
                    "2025-02-25",
                    5.86
                ],
                [
                    "2025-02-26",
                    9.95
                ],
                [
                    "2025-02-27",
                    17.68
                ],
                [
                    "2025-02-28",
                    9.24
                ],
                [
                    "2025-03-03",
                    -4.18
                ],
                [
                    "2025-03-04",
                    -2.72
                ],
                [
                    "2025-03-05",
                    1.96
                ],
                [
                    "2025-03-06",
                    -12.44
                ],
                [
                    "2025-03-07",
                    2.45
                ],
                [
                    "2025-03-10",
                    3.97
                ],
                [
                    "2025-03-11",
                    4.07
                ],
                [
                    "2025-03-12",
                    6.33
                ],
                [
                    "2025-03-13",
                    6.86
                ],
                [
                    "2025-03-14",
                    3.76
                ],
                [
                    "2025-03-17",
                    -9.52
                ],
                [
                    "2025-03-18",
                    -1.8
                ],
                [
                    "2025-03-19",
                    -10.71
                ],
                [
                    "2025-03-20",
                    5.54
                ],
                [
                    "2025-03-21",
                    12.37
                ],
                [
                    "2025-03-24",
                    7.53
                ],
                [
                    "2025-03-25",
                    0.46
                ],
                [
                    "2025-03-26",
                    -3.76
                ],
                [
                    "2025-03-27",
                    -0.8
                ],
                [
                    "2025-03-28",
                    8.02
                ],
                [
                    "2025-03-31",
                    -8.5
                ],
                [
                    "2025-04-01",
                    -7.8
                ],
                [
                    "2025-04-02",
                    -6.43
                ],
                [
                    "2025-04-03",
                    -8.62
                ],
                [
                    "2025-04-07",
                    10.78
                ],
                [
                    "2025-04-08",
                    -0.04
                ],
                [
                    "2025-04-09",
                    -11.23
                ],
                [
                    "2025-04-10",
                    -3.66
                ],
                [
                    "2025-04-11",
                    -5.36
                ],
                [
                    "2025-04-14",
                    -7.43
                ],
                [
                    "2025-04-15",
                    11.78
                ],
                [
                    "2025-04-16",
                    -3.38
                ],
                [
                    "2025-04-17",
                    -15.71
                ],
                [
                    "2025-04-18",
                    8.38
                ],
                [
                    "2025-04-21",
                    -2.57
                ],
                [
                    "2025-04-22",
                    -0.33
                ],
                [
                    "2025-04-23",
                    7.89
                ],
                [
                    "2025-04-24",
                    7.63
                ],
                [
                    "2025-04-25",
                    2.35
                ],
                [
                    "2025-04-28",
                    14.79
                ],
                [
                    "2025-04-29",
                    -6.77
                ],
                [
                    "2025-04-30",
                    0.45
                ],
                [
                    "2025-05-06",
                    1.85
                ],
                [
                    "2025-05-07",
                    -2.45
                ],
                [
                    "2025-05-08",
                    4.62
                ],
                [
                    "2025-05-09",
                    11.89
                ],
                [
                    "2025-05-12",
                    3.16
                ],
                [
                    "2025-05-13",
                    6.36
                ],
                [
                    "2025-05-14",
                    0.1
                ],
                [
                    "2025-05-15",
                    13.31
                ],
                [
                    "2025-05-16",
                    6.99
                ],
                [
                    "2025-05-19",
                    -4.35
                ],
                [
                    "2025-05-20",
                    5.12
                ],
                [
                    "2025-05-21",
                    4.69
                ],
                [
                    "2025-05-22",
                    17.74
                ],
                [
                    "2025-05-23",
                    9.0
                ],
                [
                    "2025-05-26",
                    0.95
                ],
                [
                    "2025-05-27",
                    5.51
                ],
                [
                    "2025-05-28",
                    10.72
                ],
                [
                    "2025-05-29",
                    3.3
                ],
                [
                    "2025-05-30",
                    5.28
                ],
                [
                    "2025-06-03",
                    3.08
                ],
                [
                    "2025-06-04",
                    -4.74
                ],
                [
                    "2025-06-05",
                    -4.64
                ],
                [
                    "2025-06-06",
                    3.1
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2025-01-27",
                    7.46
                ],
                [
                    "2025-02-05",
                    14.07
                ],
                [
                    "2025-02-06",
                    0.96
                ],
                [
                    "2025-02-07",
                    1.62
                ],
                [
                    "2025-02-10",
                    6.0
                ],
                [
                    "2025-02-11",
                    -6.7
                ],
                [
                    "2025-03-06",
                    18.47
                ],
                [
                    "2025-03-07",
                    -5.49
                ],
                [
                    "2025-03-10",
                    -1.4
                ],
                [
                    "2025-03-31",
                    11.13
                ],
                [
                    "2025-04-01",
                    4.46
                ],
                [
                    "2025-04-02",
                    6.52
                ],
                [
                    "2025-04-03",
                    6.66
                ],
                [
                    "2025-04-07",
                    -10.88
                ],
                [
                    "2025-04-08",
                    -1.07
                ],
                [
                    "2025-04-09",
                    8.35
                ],
                [
                    "2025-04-10",
                    1.14
                ],
                [
                    "2025-04-11",
                    3.75
                ],
                [
                    "2025-04-14",
                    3.76
                ],
                [
                    "2025-04-15",
                    -9.36
                ],
                [
                    "2025-04-16",
                    1.99
                ],
                [
                    "2025-04-17",
                    27.91
                ],
                [
                    "2025-04-18",
                    -7.41
                ],
                [
                    "2025-04-21",
                    0.07
                ],
                [
                    "2025-04-22",
                    -1.68
                ],
                [
                    "2025-04-23",
                    -9.59
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-01-24",
                    10.5
                ],
                [
                    "2025-01-27",
                    3.51
                ],
                [
                    "2025-02-05",
                    -7.16
                ],
                [
                    "2025-02-06",
                    -6.56
                ],
                [
                    "2025-03-06",
                    -6.03
                ],
                [
                    "2025-03-07",
                    3.03
                ],
                [
                    "2025-03-10",
                    -2.57
                ],
                [
                    "2025-03-19",
                    2.2
                ],
                [
                    "2025-03-20",
                    1.54
                ],
                [
                    "2025-03-21",
                    -3.2
                ],
                [
                    "2025-04-01",
                    3.35
                ],
                [
                    "2025-04-02",
                    -0.09
                ],
                [
                    "2025-04-03",
                    1.95
                ],
                [
                    "2025-04-07",
                    0.1
                ],
                [
                    "2025-04-08",
                    1.11
                ],
                [
                    "2025-04-09",
                    2.88
                ],
                [
                    "2025-04-10",
                    2.53
                ],
                [
                    "2025-04-11",
                    1.61
                ],
                [
                    "2025-04-14",
                    3.67
                ],
                [
                    "2025-04-15",
                    -2.42
                ],
                [
                    "2025-04-16",
                    1.39
                ],
                [
                    "2025-05-08",
                    0.31
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2024-12-11",
                    11.72
                ],
                [
                    "2024-12-12",
                    5.08
                ],
                [
                    "2024-12-13",
                    -2.91
                ],
                [
                    "2024-12-16",
                    4.6
                ],
                [
                    "2024-12-17",
                    13.48
                ],
                [
                    "2024-12-18",
                    5.16
                ],
                [
                    "2024-12-19",
                    7.88
                ],
                [
                    "2024-12-20",
                    4.98
                ],
                [
                    "2024-12-23",
                    8.02
                ],
                [
                    "2024-12-24",
                    6.91
                ],
                [
                    "2024-12-25",
                    22.97
                ],
                [
                    "2024-12-26",
                    8.99
                ],
                [
                    "2024-12-27",
                    -2.49
                ],
                [
                    "2024-12-30",
                    2.85
                ],
                [
                    "2024-12-31",
                    -2.03
                ],
                [
                    "2025-01-02",
                    -3.67
                ],
                [
                    "2025-01-03",
                    7.03
                ],
                [
                    "2025-01-06",
                    15.0
                ],
                [
                    "2025-01-07",
                    0.28
                ],
                [
                    "2025-01-08",
                    3.29
                ],
                [
                    "2025-01-09",
                    -2.9
                ],
                [
                    "2025-01-10",
                    8.29
                ],
                [
                    "2025-01-13",
                    9.65
                ],
                [
                    "2025-01-14",
                    -2.92
                ],
                [
                    "2025-01-15",
                    0.99
                ],
                [
                    "2025-01-16",
                    0.37
                ],
                [
                    "2025-01-17",
                    3.72
                ],
                [
                    "2025-01-20",
                    5.22
                ],
                [
                    "2025-01-21",
                    -5.25
                ],
                [
                    "2025-01-22",
                    11.82
                ],
                [
                    "2025-01-23",
                    -7.58
                ],
                [
                    "2025-02-13",
                    6.18
                ],
                [
                    "2025-02-14",
                    -0.1
                ],
                [
                    "2025-02-17",
                    7.35
                ],
                [
                    "2025-02-18",
                    16.97
                ],
                [
                    "2025-02-19",
                    -4.78
                ],
                [
                    "2025-02-20",
                    -3.08
                ],
                [
                    "2025-02-21",
                    6.83
                ],
                [
                    "2025-02-24",
                    14.74
                ],
                [
                    "2025-02-25",
                    5.86
                ],
                [
                    "2025-02-26",
                    9.95
                ],
                [
                    "2025-02-27",
                    17.68
                ],
                [
                    "2025-02-28",
                    9.24
                ],
                [
                    "2025-03-03",
                    -4.18
                ],
                [
                    "2025-03-04",
                    -2.72
                ],
                [
                    "2025-03-05",
                    1.96
                ],
                [
                    "2025-03-12",
                    6.33
                ],
                [
                    "2025-03-13",
                    6.86
                ],
                [
                    "2025-03-14",
                    3.76
                ],
                [
                    "2025-03-17",
                    -9.52
                ],
                [
                    "2025-03-18",
                    -1.8
                ],
                [
                    "2025-03-24",
                    7.53
                ],
                [
                    "2025-03-25",
                    0.46
                ],
                [
                    "2025-03-26",
                    -3.76
                ],
                [
                    "2025-03-27",
                    -0.8
                ],
                [
                    "2025-03-28",
                    8.02
                ],
                [
                    "2025-04-24",
                    7.63
                ],
                [
                    "2025-04-25",
                    2.35
                ],
                [
                    "2025-04-28",
                    14.79
                ],
                [
                    "2025-04-29",
                    -6.77
                ],
                [
                    "2025-04-30",
                    0.45
                ],
                [
                    "2025-05-06",
                    1.85
                ],
                [
                    "2025-05-07",
                    -2.45
                ],
                [
                    "2025-05-09",
                    11.89
                ],
                [
                    "2025-05-12",
                    3.16
                ],
                [
                    "2025-05-13",
                    6.36
                ],
                [
                    "2025-05-14",
                    0.1
                ],
                [
                    "2025-05-15",
                    13.31
                ],
                [
                    "2025-05-16",
                    6.99
                ],
                [
                    "2025-05-19",
                    -4.35
                ],
                [
                    "2025-05-20",
                    5.12
                ],
                [
                    "2025-05-21",
                    4.69
                ],
                [
                    "2025-05-22",
                    17.74
                ],
                [
                    "2025-05-23",
                    9.0
                ],
                [
                    "2025-05-26",
                    0.95
                ],
                [
                    "2025-05-27",
                    5.51
                ],
                [
                    "2025-05-28",
                    10.72
                ],
                [
                    "2025-05-29",
                    3.3
                ],
                [
                    "2025-05-30",
                    5.28
                ],
                [
                    "2025-06-03",
                    3.08
                ],
                [
                    "2025-06-04",
                    -4.74
                ],
                [
                    "2025-06-06",
                    3.1
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-02",
                "2024-12-03",
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002410 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_880aa8f0a91349f485b6af755fc8e624.setOption(option_880aa8f0a91349f485b6af755fc8e624);
            window.addEventListener('resize', function(){
                chart_880aa8f0a91349f485b6af755fc8e624.resize();
            })
    </script>

<script>
// 等待页面完全加载
window.addEventListener('load', function() {
    setTimeout(function() {
        // 获取所有图表实例
        var charts = [];
        var chartDoms = document.querySelectorAll('[_echarts_instance_]');
        chartDoms.forEach(function(dom) {
            var chart = echarts.getInstanceByDom(dom);
            if (chart) {
                charts.push(chart);
            }
        });

        console.log('找到图表数量:', charts.length);

        if (charts.length >= 2) {
            // 确保缩放联动
            echarts.connect(charts);
            console.log('缩放联动已设置');

            var chart1 = charts[0];
            var chart2 = charts[1];

            // 简单的鼠标悬停联动
            var isUpdating = false;

            chart1.on('mousemove', function(params) {
                if (!isUpdating && params.dataIndex !== undefined) {
                    isUpdating = true;
                    chart2.dispatchAction({
                        type: 'showTip',
                        seriesIndex: 0,
                        dataIndex: params.dataIndex
                    });
                    setTimeout(function() { isUpdating = false; }, 10);
                }
            });

            chart2.on('mousemove', function(params) {
                if (!isUpdating && params.dataIndex !== undefined) {
                    isUpdating = true;
                    chart1.dispatchAction({
                        type: 'showTip',
                        seriesIndex: 0,
                        dataIndex: params.dataIndex
                    });
                    setTimeout(function() { isUpdating = false; }, 10);
                }
            });

            console.log('鼠标悬停联动已设置');
        }
    }, 2000);
});
</script>

</body>
</html>
