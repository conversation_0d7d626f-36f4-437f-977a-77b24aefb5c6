<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="f05df84ca83340ba820673dac4e970bb" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_f05df84ca83340ba820673dac4e970bb = echarts.init(
            document.getElementById('f05df84ca83340ba820673dac4e970bb'), 'white', {renderer: 'canvas'});
            
    // 等待页面加载完成
    setTimeout(function() {
        // 获取所有图表实例
        var charts = [];
        var chartDoms = document.querySelectorAll('[_echarts_instance_]');
        chartDoms.forEach(function(dom) {
            var chart = echarts.getInstanceByDom(dom);
            if (chart) {
                charts.push(chart);
            }
        });

        console.log('找到图表数量:', charts.length);

        // 设置缩放联动
        if (charts.length >= 2) {
            echarts.connect(charts);
            console.log('图表缩放联动已设置');
        }
    }, 100);

        var option_f05df84ca83340ba820673dac4e970bb = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    22.17,
                    22.2,
                    22.03,
                    22.27
                ],
                [
                    22.25,
                    22.31,
                    22.2,
                    22.52
                ],
                [
                    22.28,
                    22.12,
                    22.0,
                    22.47
                ],
                [
                    22.03,
                    22.15,
                    22.01,
                    22.19
                ],
                [
                    22.1,
                    22.21,
                    21.98,
                    22.24
                ],
                [
                    22.45,
                    22.65,
                    22.44,
                    22.88
                ],
                [
                    23.0,
                    22.5,
                    22.47,
                    23.12
                ],
                [
                    22.65,
                    22.68,
                    22.52,
                    22.78
                ],
                [
                    22.78,
                    22.9,
                    22.74,
                    23.12
                ],
                [
                    22.7,
                    22.43,
                    22.37,
                    22.7
                ],
                [
                    22.35,
                    22.17,
                    22.08,
                    22.46
                ],
                [
                    22.2,
                    22.0,
                    21.92,
                    22.23
                ],
                [
                    22.1,
                    21.84,
                    21.8,
                    22.19
                ],
                [
                    21.72,
                    21.53,
                    21.25,
                    21.72
                ],
                [
                    21.4,
                    21.5,
                    21.38,
                    21.63
                ],
                [
                    21.5,
                    21.13,
                    21.07,
                    21.6
                ],
                [
                    21.06,
                    21.18,
                    20.9,
                    21.19
                ],
                [
                    21.18,
                    20.98,
                    20.92,
                    21.2
                ],
                [
                    21.0,
                    21.26,
                    20.99,
                    21.35
                ],
                [
                    21.21,
                    21.3,
                    21.15,
                    21.39
                ],
                [
                    21.3,
                    21.09,
                    21.01,
                    21.3
                ],
                [
                    21.1,
                    20.88,
                    20.74,
                    21.23
                ],
                [
                    20.88,
                    21.09,
                    20.78,
                    21.5
                ],
                [
                    21.37,
                    22.47,
                    21.28,
                    23.01
                ],
                [
                    22.0,
                    22.02,
                    21.34,
                    22.63
                ],
                [
                    21.8,
                    22.23,
                    21.61,
                    22.43
                ],
                [
                    22.0,
                    22.32,
                    21.9,
                    22.74
                ],
                [
                    22.04,
                    22.48,
                    21.8,
                    22.7
                ],
                [
                    22.88,
                    22.07,
                    22.01,
                    23.0
                ],
                [
                    21.76,
                    22.22,
                    21.45,
                    22.48
                ],
                [
                    22.35,
                    22.19,
                    21.78,
                    22.35
                ],
                [
                    22.11,
                    21.89,
                    21.54,
                    22.15
                ],
                [
                    21.99,
                    22.02,
                    21.87,
                    22.5
                ],
                [
                    22.0,
                    21.83,
                    21.74,
                    22.28
                ],
                [
                    21.71,
                    21.63,
                    21.37,
                    21.82
                ],
                [
                    21.55,
                    21.58,
                    21.31,
                    21.72
                ],
                [
                    21.7,
                    21.94,
                    21.56,
                    22.1
                ],
                [
                    21.85,
                    21.66,
                    21.64,
                    22.1
                ],
                [
                    21.58,
                    21.8,
                    21.33,
                    21.8
                ],
                [
                    21.8,
                    21.58,
                    21.52,
                    22.0
                ],
                [
                    22.08,
                    22.76,
                    21.9,
                    22.8
                ],
                [
                    22.63,
                    22.45,
                    22.33,
                    22.63
                ],
                [
                    22.4,
                    22.51,
                    22.28,
                    22.73
                ],
                [
                    22.66,
                    23.16,
                    22.49,
                    23.28
                ],
                [
                    23.6,
                    23.33,
                    23.15,
                    24.06
                ],
                [
                    22.69,
                    22.69,
                    22.49,
                    22.88
                ],
                [
                    22.69,
                    22.56,
                    22.5,
                    22.97
                ],
                [
                    22.74,
                    22.48,
                    22.37,
                    22.8
                ],
                [
                    21.7,
                    21.78,
                    21.63,
                    21.97
                ],
                [
                    21.84,
                    21.78,
                    21.67,
                    22.02
                ],
                [
                    22.1,
                    21.9,
                    21.8,
                    22.16
                ],
                [
                    21.84,
                    21.94,
                    21.7,
                    22.02
                ],
                [
                    21.9,
                    21.56,
                    21.5,
                    21.98
                ],
                [
                    21.44,
                    21.96,
                    21.15,
                    22.11
                ],
                [
                    21.95,
                    21.67,
                    21.64,
                    22.08
                ],
                [
                    21.68,
                    21.73,
                    21.63,
                    22.3
                ],
                [
                    21.73,
                    21.82,
                    21.56,
                    22.13
                ],
                [
                    21.7,
                    21.39,
                    21.38,
                    21.79
                ],
                [
                    21.53,
                    21.34,
                    21.24,
                    21.65
                ],
                [
                    21.43,
                    21.71,
                    21.36,
                    21.73
                ],
                [
                    21.63,
                    21.89,
                    21.6,
                    21.92
                ],
                [
                    21.83,
                    21.89,
                    21.7,
                    21.99
                ],
                [
                    21.73,
                    22.13,
                    21.72,
                    22.45
                ],
                [
                    22.4,
                    22.05,
                    21.92,
                    22.4
                ],
                [
                    21.84,
                    22.11,
                    21.7,
                    22.16
                ],
                [
                    22.12,
                    22.07,
                    22.04,
                    22.39
                ],
                [
                    22.21,
                    22.15,
                    21.93,
                    22.31
                ],
                [
                    22.98,
                    22.34,
                    22.24,
                    23.1
                ],
                [
                    22.3,
                    22.2,
                    22.17,
                    22.51
                ],
                [
                    22.3,
                    22.79,
                    22.2,
                    22.99
                ],
                [
                    22.79,
                    23.3,
                    22.71,
                    23.54
                ],
                [
                    23.58,
                    22.89,
                    22.83,
                    23.73
                ],
                [
                    22.71,
                    22.38,
                    22.23,
                    22.98
                ],
                [
                    22.38,
                    22.18,
                    21.86,
                    22.43
                ],
                [
                    22.1,
                    22.35,
                    21.98,
                    22.8
                ],
                [
                    22.36,
                    22.35,
                    22.17,
                    22.72
                ],
                [
                    22.25,
                    22.35,
                    22.03,
                    22.48
                ],
                [
                    22.9,
                    23.84,
                    22.49,
                    24.14
                ],
                [
                    23.69,
                    24.88,
                    23.51,
                    25.0
                ],
                [
                    24.44,
                    23.91,
                    23.8,
                    24.79
                ],
                [
                    23.74,
                    23.09,
                    22.98,
                    23.84
                ],
                [
                    23.6,
                    24.22,
                    23.17,
                    24.5
                ],
                [
                    21.82,
                    21.8,
                    21.8,
                    22.95
                ],
                [
                    21.45,
                    21.58,
                    20.88,
                    21.98
                ],
                [
                    21.0,
                    21.82,
                    19.8,
                    21.99
                ],
                [
                    22.3,
                    24.0,
                    22.17,
                    24.0
                ],
                [
                    25.38,
                    26.4,
                    25.3,
                    26.4
                ],
                [
                    25.33,
                    26.53,
                    25.33,
                    27.0
                ],
                [
                    26.11,
                    25.79,
                    25.41,
                    26.45
                ],
                [
                    26.52,
                    26.43,
                    25.65,
                    26.72
                ],
                [
                    27.28,
                    25.11,
                    25.05,
                    27.49
                ],
                [
                    25.11,
                    24.47,
                    24.26,
                    25.5
                ],
                [
                    25.11,
                    26.92,
                    24.99,
                    26.92
                ],
                [
                    27.3,
                    28.56,
                    27.04,
                    29.38
                ],
                [
                    26.2,
                    25.9,
                    25.83,
                    27.5
                ],
                [
                    26.97,
                    25.84,
                    25.69,
                    26.99
                ],
                [
                    26.1,
                    24.81,
                    24.52,
                    26.22
                ],
                [
                    24.33,
                    24.19,
                    23.93,
                    24.48
                ],
                [
                    24.49,
                    24.33,
                    24.15,
                    24.69
                ],
                [
                    24.15,
                    24.08,
                    23.85,
                    24.65
                ],
                [
                    24.77,
                    25.8,
                    24.72,
                    26.26
                ],
                [
                    25.1,
                    25.9,
                    25.1,
                    26.0
                ],
                [
                    25.78,
                    25.4,
                    25.27,
                    26.27
                ],
                [
                    24.9,
                    25.12,
                    24.58,
                    25.31
                ],
                [
                    24.31,
                    24.34,
                    23.92,
                    24.45
                ],
                [
                    24.34,
                    24.44,
                    23.94,
                    24.55
                ],
                [
                    24.2,
                    24.06,
                    23.9,
                    24.42
                ],
                [
                    23.62,
                    23.66,
                    23.35,
                    23.89
                ],
                [
                    24.01,
                    23.71,
                    23.65,
                    24.24
                ],
                [
                    23.99,
                    23.93,
                    23.6,
                    24.35
                ],
                [
                    23.76,
                    23.96,
                    23.76,
                    24.16
                ],
                [
                    24.72,
                    25.4,
                    24.24,
                    25.4
                ],
                [
                    24.78,
                    24.25,
                    24.09,
                    25.28
                ],
                [
                    23.81,
                    24.8,
                    23.52,
                    25.46
                ],
                [
                    24.0,
                    24.43,
                    23.92,
                    24.71
                ],
                [
                    24.18,
                    23.7,
                    23.68,
                    24.3
                ],
                [
                    23.6,
                    23.77,
                    23.56,
                    24.0
                ],
                [
                    23.34,
                    23.34,
                    23.01,
                    23.44
                ],
                [
                    23.53,
                    23.24,
                    23.12,
                    23.59
                ],
                [
                    23.82,
                    24.17,
                    23.81,
                    24.62
                ],
                [
                    23.8,
                    24.12,
                    23.8,
                    24.39
                ],
                [
                    24.18,
                    23.91,
                    23.81,
                    24.3
                ],
                [
                    23.85,
                    24.15,
                    23.82,
                    24.43
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    -14.61
                ],
                [
                    "2024-12-06",
                    -10.32
                ],
                [
                    "2024-12-09",
                    1.65
                ],
                [
                    "2024-12-10",
                    -4.05
                ],
                [
                    "2024-12-11",
                    -9.83
                ],
                [
                    "2024-12-12",
                    -2.17
                ],
                [
                    "2024-12-13",
                    -11.32
                ],
                [
                    "2024-12-16",
                    -1.62
                ],
                [
                    "2024-12-17",
                    3.03
                ],
                [
                    "2024-12-18",
                    0.03
                ],
                [
                    "2024-12-19",
                    -4.75
                ],
                [
                    "2024-12-20",
                    -2.19
                ],
                [
                    "2024-12-23",
                    5.25
                ],
                [
                    "2024-12-24",
                    -2.72
                ],
                [
                    "2024-12-25",
                    -8.76
                ],
                [
                    "2024-12-26",
                    6.82
                ],
                [
                    "2024-12-27",
                    8.37
                ],
                [
                    "2024-12-30",
                    -1.46
                ],
                [
                    "2024-12-31",
                    -4.9
                ],
                [
                    "2025-01-02",
                    3.75
                ],
                [
                    "2025-01-03",
                    -0.89
                ],
                [
                    "2025-01-06",
                    1.25
                ],
                [
                    "2025-01-07",
                    -1.22
                ],
                [
                    "2025-01-08",
                    -0.12
                ],
                [
                    "2025-01-09",
                    7.14
                ],
                [
                    "2025-01-10",
                    3.18
                ],
                [
                    "2025-01-13",
                    1.33
                ],
                [
                    "2025-01-14",
                    -10.37
                ],
                [
                    "2025-01-15",
                    -8.81
                ],
                [
                    "2025-01-16",
                    -0.85
                ],
                [
                    "2025-01-17",
                    -10.58
                ],
                [
                    "2025-01-20",
                    -11.12
                ],
                [
                    "2025-01-21",
                    -11.92
                ],
                [
                    "2025-01-22",
                    11.43
                ],
                [
                    "2025-01-23",
                    -6.6
                ],
                [
                    "2025-01-24",
                    -1.7
                ],
                [
                    "2025-01-27",
                    0.34
                ],
                [
                    "2025-02-05",
                    1.33
                ],
                [
                    "2025-02-06",
                    -9.07
                ],
                [
                    "2025-02-07",
                    -3.86
                ],
                [
                    "2025-02-10",
                    5.77
                ],
                [
                    "2025-02-11",
                    0.44
                ],
                [
                    "2025-02-12",
                    -11.62
                ],
                [
                    "2025-02-13",
                    -4.81
                ],
                [
                    "2025-02-14",
                    -10.06
                ],
                [
                    "2025-02-17",
                    -10.13
                ],
                [
                    "2025-02-18",
                    6.14
                ],
                [
                    "2025-02-19",
                    5.48
                ],
                [
                    "2025-02-20",
                    -21.06
                ],
                [
                    "2025-02-21",
                    -6.88
                ],
                [
                    "2025-02-24",
                    11.26
                ],
                [
                    "2025-02-25",
                    -0.75
                ],
                [
                    "2025-02-26",
                    4.51
                ],
                [
                    "2025-02-27",
                    12.23
                ],
                [
                    "2025-02-28",
                    -4.25
                ],
                [
                    "2025-03-03",
                    1.52
                ],
                [
                    "2025-03-04",
                    -1.46
                ],
                [
                    "2025-03-05",
                    -3.96
                ],
                [
                    "2025-03-06",
                    -9.39
                ],
                [
                    "2025-03-07",
                    -2.51
                ],
                [
                    "2025-03-10",
                    -6.74
                ],
                [
                    "2025-03-11",
                    -6.19
                ],
                [
                    "2025-03-12",
                    -3.07
                ],
                [
                    "2025-03-13",
                    1.92
                ],
                [
                    "2025-03-14",
                    -1.88
                ],
                [
                    "2025-03-17",
                    -6.74
                ],
                [
                    "2025-03-18",
                    -9.36
                ],
                [
                    "2025-03-19",
                    2.06
                ],
                [
                    "2025-03-20",
                    -9.18
                ],
                [
                    "2025-03-21",
                    -18.0
                ],
                [
                    "2025-03-24",
                    5.48
                ],
                [
                    "2025-03-25",
                    3.09
                ],
                [
                    "2025-03-26",
                    -0.28
                ],
                [
                    "2025-03-27",
                    0.84
                ],
                [
                    "2025-03-28",
                    14.17
                ],
                [
                    "2025-03-31",
                    3.06
                ],
                [
                    "2025-04-01",
                    -9.41
                ],
                [
                    "2025-04-02",
                    -7.92
                ],
                [
                    "2025-04-03",
                    6.68
                ],
                [
                    "2025-04-07",
                    -3.27
                ],
                [
                    "2025-04-08",
                    -10.52
                ],
                [
                    "2025-04-09",
                    -3.8
                ],
                [
                    "2025-04-10",
                    14.97
                ],
                [
                    "2025-04-11",
                    10.94
                ],
                [
                    "2025-04-14",
                    -9.15
                ],
                [
                    "2025-04-15",
                    -0.31
                ],
                [
                    "2025-04-16",
                    5.96
                ],
                [
                    "2025-04-17",
                    -20.31
                ],
                [
                    "2025-04-18",
                    -2.75
                ],
                [
                    "2025-04-21",
                    12.7
                ],
                [
                    "2025-04-22",
                    -8.03
                ],
                [
                    "2025-04-23",
                    -8.57
                ],
                [
                    "2025-04-24",
                    3.24
                ],
                [
                    "2025-04-25",
                    -12.34
                ],
                [
                    "2025-04-28",
                    -1.25
                ],
                [
                    "2025-04-29",
                    5.57
                ],
                [
                    "2025-04-30",
                    -9.56
                ],
                [
                    "2025-05-06",
                    10.21
                ],
                [
                    "2025-05-07",
                    2.12
                ],
                [
                    "2025-05-08",
                    -7.25
                ],
                [
                    "2025-05-09",
                    -6.28
                ],
                [
                    "2025-05-12",
                    -13.34
                ],
                [
                    "2025-05-13",
                    12.64
                ],
                [
                    "2025-05-14",
                    -5.39
                ],
                [
                    "2025-05-15",
                    -6.38
                ],
                [
                    "2025-05-16",
                    4.33
                ],
                [
                    "2025-05-19",
                    -1.78
                ],
                [
                    "2025-05-20",
                    0.21
                ],
                [
                    "2025-05-21",
                    7.08
                ],
                [
                    "2025-05-22",
                    -3.57
                ],
                [
                    "2025-05-23",
                    0.53
                ],
                [
                    "2025-05-26",
                    -9.08
                ],
                [
                    "2025-05-27",
                    -8.71
                ],
                [
                    "2025-05-28",
                    -6.01
                ],
                [
                    "2025-05-29",
                    -6.25
                ],
                [
                    "2025-05-30",
                    3.68
                ],
                [
                    "2025-06-03",
                    5.78
                ],
                [
                    "2025-06-04",
                    -7.52
                ],
                [
                    "2025-06-05",
                    -3.12
                ],
                [
                    "2025-06-06",
                    -1.19
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    -1.03
                ],
                [
                    "2024-12-06",
                    1.16
                ],
                [
                    "2024-12-09",
                    3.15
                ],
                [
                    "2024-12-10",
                    -1.6
                ],
                [
                    "2024-12-11",
                    -0.17
                ],
                [
                    "2024-12-12",
                    -1.59
                ],
                [
                    "2024-12-13",
                    -8.94
                ],
                [
                    "2024-12-16",
                    -4.61
                ],
                [
                    "2024-12-17",
                    -9.36
                ],
                [
                    "2024-12-18",
                    1.01
                ],
                [
                    "2024-12-19",
                    3.7
                ],
                [
                    "2024-12-20",
                    -1.02
                ],
                [
                    "2024-12-23",
                    2.78
                ],
                [
                    "2024-12-24",
                    4.29
                ],
                [
                    "2024-12-25",
                    7.25
                ],
                [
                    "2024-12-26",
                    -1.44
                ],
                [
                    "2024-12-27",
                    -6.06
                ],
                [
                    "2024-12-30",
                    4.22
                ],
                [
                    "2024-12-31",
                    2.6
                ],
                [
                    "2025-01-02",
                    -0.51
                ],
                [
                    "2025-01-03",
                    3.02
                ],
                [
                    "2025-01-06",
                    0.66
                ],
                [
                    "2025-01-07",
                    2.65
                ],
                [
                    "2025-01-08",
                    2.19
                ],
                [
                    "2025-01-09",
                    -2.72
                ],
                [
                    "2025-01-10",
                    -0.3
                ],
                [
                    "2025-01-13",
                    0.23
                ],
                [
                    "2025-01-14",
                    7.37
                ],
                [
                    "2025-01-15",
                    4.06
                ],
                [
                    "2025-01-16",
                    0.55
                ],
                [
                    "2025-01-17",
                    1.94
                ],
                [
                    "2025-01-20",
                    0.25
                ],
                [
                    "2025-01-21",
                    3.28
                ],
                [
                    "2025-01-22",
                    -4.61
                ],
                [
                    "2025-01-23",
                    1.9
                ],
                [
                    "2025-01-24",
                    -4.48
                ],
                [
                    "2025-01-27",
                    -2.77
                ],
                [
                    "2025-02-05",
                    -3.09
                ],
                [
                    "2025-02-06",
                    4.5
                ],
                [
                    "2025-02-07",
                    -0.04
                ],
                [
                    "2025-02-10",
                    -3.97
                ],
                [
                    "2025-02-11",
                    -0.8
                ],
                [
                    "2025-02-12",
                    0.15
                ],
                [
                    "2025-02-13",
                    -1.67
                ],
                [
                    "2025-02-14",
                    2.06
                ],
                [
                    "2025-02-17",
                    0.82
                ],
                [
                    "2025-02-18",
                    -1.3
                ],
                [
                    "2025-02-19",
                    -4.63
                ],
                [
                    "2025-02-20",
                    12.33
                ],
                [
                    "2025-02-21",
                    -3.17
                ],
                [
                    "2025-02-24",
                    -8.04
                ],
                [
                    "2025-02-25",
                    -4.29
                ],
                [
                    "2025-02-26",
                    -5.89
                ],
                [
                    "2025-02-27",
                    -3.45
                ],
                [
                    "2025-02-28",
                    -1.4
                ],
                [
                    "2025-03-03",
                    2.79
                ],
                [
                    "2025-03-04",
                    -5.86
                ],
                [
                    "2025-03-05",
                    -5.78
                ],
                [
                    "2025-03-06",
                    -0.64
                ],
                [
                    "2025-03-07",
                    1.33
                ],
                [
                    "2025-03-10",
                    -0.55
                ],
                [
                    "2025-03-11",
                    -6.77
                ],
                [
                    "2025-03-12",
                    0.21
                ],
                [
                    "2025-03-13",
                    3.46
                ],
                [
                    "2025-03-14",
                    8.64
                ],
                [
                    "2025-03-17",
                    -4.45
                ],
                [
                    "2025-03-18",
                    5.68
                ],
                [
                    "2025-03-19",
                    -0.29
                ],
                [
                    "2025-03-20",
                    -3.61
                ],
                [
                    "2025-03-21",
                    -1.09
                ],
                [
                    "2025-03-24",
                    -5.4
                ],
                [
                    "2025-03-25",
                    -1.38
                ],
                [
                    "2025-03-26",
                    0.48
                ],
                [
                    "2025-03-27",
                    0.46
                ],
                [
                    "2025-03-28",
                    -3.36
                ],
                [
                    "2025-03-31",
                    -0.39
                ],
                [
                    "2025-04-01",
                    2.77
                ],
                [
                    "2025-04-02",
                    -2.6
                ],
                [
                    "2025-04-03",
                    1.05
                ],
                [
                    "2025-04-07",
                    -1.57
                ],
                [
                    "2025-04-08",
                    -0.35
                ],
                [
                    "2025-04-09",
                    -2.99
                ],
                [
                    "2025-04-10",
                    -11.92
                ],
                [
                    "2025-04-11",
                    -8.53
                ],
                [
                    "2025-04-14",
                    2.69
                ],
                [
                    "2025-04-15",
                    0.56
                ],
                [
                    "2025-04-16",
                    -1.35
                ],
                [
                    "2025-04-17",
                    5.16
                ],
                [
                    "2025-04-18",
                    -1.74
                ],
                [
                    "2025-04-21",
                    -6.02
                ],
                [
                    "2025-04-22",
                    0.54
                ],
                [
                    "2025-04-23",
                    -1.25
                ],
                [
                    "2025-04-24",
                    -2.72
                ],
                [
                    "2025-04-25",
                    0.75
                ],
                [
                    "2025-04-28",
                    -1.57
                ],
                [
                    "2025-04-29",
                    0.26
                ],
                [
                    "2025-04-30",
                    -0.47
                ],
                [
                    "2025-05-06",
                    -5.16
                ],
                [
                    "2025-05-07",
                    1.44
                ],
                [
                    "2025-05-08",
                    -1.15
                ],
                [
                    "2025-05-09",
                    -2.26
                ],
                [
                    "2025-05-12",
                    -1.22
                ],
                [
                    "2025-05-13",
                    -3.59
                ],
                [
                    "2025-05-14",
                    -1.54
                ],
                [
                    "2025-05-15",
                    -4.62
                ],
                [
                    "2025-05-16",
                    0.98
                ],
                [
                    "2025-05-19",
                    -0.99
                ],
                [
                    "2025-05-20",
                    -4.38
                ],
                [
                    "2025-05-21",
                    0.3
                ],
                [
                    "2025-05-22",
                    1.2
                ],
                [
                    "2025-05-23",
                    -2.61
                ],
                [
                    "2025-05-26",
                    3.52
                ],
                [
                    "2025-05-27",
                    -2.61
                ],
                [
                    "2025-05-28",
                    -0.51
                ],
                [
                    "2025-05-29",
                    -2.64
                ],
                [
                    "2025-05-30",
                    -3.27
                ],
                [
                    "2025-06-03",
                    -0.31
                ],
                [
                    "2025-06-04",
                    -0.48
                ],
                [
                    "2025-06-05",
                    -3.36
                ],
                [
                    "2025-06-06",
                    0.03
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    15.63
                ],
                [
                    "2024-12-06",
                    9.16
                ],
                [
                    "2024-12-09",
                    -4.8
                ],
                [
                    "2024-12-10",
                    5.65
                ],
                [
                    "2024-12-11",
                    10.0
                ],
                [
                    "2024-12-12",
                    3.75
                ],
                [
                    "2024-12-13",
                    20.26
                ],
                [
                    "2024-12-16",
                    6.24
                ],
                [
                    "2024-12-17",
                    6.33
                ],
                [
                    "2024-12-18",
                    -1.03
                ],
                [
                    "2024-12-19",
                    1.06
                ],
                [
                    "2024-12-20",
                    3.22
                ],
                [
                    "2024-12-23",
                    -8.02
                ],
                [
                    "2024-12-24",
                    -1.57
                ],
                [
                    "2024-12-25",
                    1.51
                ],
                [
                    "2024-12-26",
                    -5.39
                ],
                [
                    "2024-12-27",
                    -2.31
                ],
                [
                    "2024-12-30",
                    -2.77
                ],
                [
                    "2024-12-31",
                    2.29
                ],
                [
                    "2025-01-02",
                    -3.24
                ],
                [
                    "2025-01-03",
                    -2.13
                ],
                [
                    "2025-01-06",
                    -1.91
                ],
                [
                    "2025-01-07",
                    -1.43
                ],
                [
                    "2025-01-08",
                    -2.06
                ],
                [
                    "2025-01-09",
                    -4.43
                ],
                [
                    "2025-01-10",
                    -2.89
                ],
                [
                    "2025-01-13",
                    -1.56
                ],
                [
                    "2025-01-14",
                    3.0
                ],
                [
                    "2025-01-15",
                    4.75
                ],
                [
                    "2025-01-16",
                    0.3
                ],
                [
                    "2025-01-17",
                    8.64
                ],
                [
                    "2025-01-20",
                    10.87
                ],
                [
                    "2025-01-21",
                    8.65
                ],
                [
                    "2025-01-22",
                    -6.82
                ],
                [
                    "2025-01-23",
                    4.69
                ],
                [
                    "2025-01-24",
                    6.18
                ],
                [
                    "2025-01-27",
                    2.43
                ],
                [
                    "2025-02-05",
                    1.75
                ],
                [
                    "2025-02-06",
                    4.57
                ],
                [
                    "2025-02-07",
                    3.91
                ],
                [
                    "2025-02-10",
                    -1.8
                ],
                [
                    "2025-02-11",
                    0.36
                ],
                [
                    "2025-02-12",
                    11.47
                ],
                [
                    "2025-02-13",
                    6.49
                ],
                [
                    "2025-02-14",
                    7.99
                ],
                [
                    "2025-02-17",
                    9.31
                ],
                [
                    "2025-02-18",
                    -4.84
                ],
                [
                    "2025-02-19",
                    -0.85
                ],
                [
                    "2025-02-20",
                    8.73
                ],
                [
                    "2025-02-21",
                    10.05
                ],
                [
                    "2025-02-24",
                    -3.22
                ],
                [
                    "2025-02-25",
                    5.04
                ],
                [
                    "2025-02-26",
                    1.38
                ],
                [
                    "2025-02-27",
                    -8.77
                ],
                [
                    "2025-02-28",
                    5.65
                ],
                [
                    "2025-03-03",
                    -4.32
                ],
                [
                    "2025-03-04",
                    7.33
                ],
                [
                    "2025-03-05",
                    9.74
                ],
                [
                    "2025-03-06",
                    10.04
                ],
                [
                    "2025-03-07",
                    1.18
                ],
                [
                    "2025-03-10",
                    7.29
                ],
                [
                    "2025-03-11",
                    12.97
                ],
                [
                    "2025-03-12",
                    2.87
                ],
                [
                    "2025-03-13",
                    -5.38
                ],
                [
                    "2025-03-14",
                    -6.76
                ],
                [
                    "2025-03-17",
                    11.19
                ],
                [
                    "2025-03-18",
                    3.68
                ],
                [
                    "2025-03-19",
                    -1.76
                ],
                [
                    "2025-03-20",
                    12.79
                ],
                [
                    "2025-03-21",
                    19.09
                ],
                [
                    "2025-03-24",
                    -0.08
                ],
                [
                    "2025-03-25",
                    -1.71
                ],
                [
                    "2025-03-26",
                    -0.19
                ],
                [
                    "2025-03-27",
                    -1.3
                ],
                [
                    "2025-03-28",
                    -10.81
                ],
                [
                    "2025-03-31",
                    -2.68
                ],
                [
                    "2025-04-01",
                    6.64
                ],
                [
                    "2025-04-02",
                    10.52
                ],
                [
                    "2025-04-03",
                    -7.72
                ],
                [
                    "2025-04-07",
                    4.84
                ],
                [
                    "2025-04-08",
                    10.88
                ],
                [
                    "2025-04-09",
                    6.79
                ],
                [
                    "2025-04-10",
                    -3.04
                ],
                [
                    "2025-04-11",
                    -2.41
                ],
                [
                    "2025-04-14",
                    6.46
                ],
                [
                    "2025-04-15",
                    -0.26
                ],
                [
                    "2025-04-16",
                    -4.62
                ],
                [
                    "2025-04-17",
                    15.16
                ],
                [
                    "2025-04-18",
                    4.49
                ],
                [
                    "2025-04-21",
                    -6.68
                ],
                [
                    "2025-04-22",
                    7.49
                ],
                [
                    "2025-04-23",
                    9.82
                ],
                [
                    "2025-04-24",
                    -0.52
                ],
                [
                    "2025-04-25",
                    11.59
                ],
                [
                    "2025-04-28",
                    2.82
                ],
                [
                    "2025-04-29",
                    -5.83
                ],
                [
                    "2025-04-30",
                    10.04
                ],
                [
                    "2025-05-06",
                    -5.05
                ],
                [
                    "2025-05-07",
                    -3.56
                ],
                [
                    "2025-05-08",
                    8.4
                ],
                [
                    "2025-05-09",
                    8.54
                ],
                [
                    "2025-05-12",
                    14.56
                ],
                [
                    "2025-05-13",
                    -9.05
                ],
                [
                    "2025-05-14",
                    6.93
                ],
                [
                    "2025-05-15",
                    10.99
                ],
                [
                    "2025-05-16",
                    -5.31
                ],
                [
                    "2025-05-19",
                    2.77
                ],
                [
                    "2025-05-20",
                    4.17
                ],
                [
                    "2025-05-21",
                    -7.37
                ],
                [
                    "2025-05-22",
                    2.37
                ],
                [
                    "2025-05-23",
                    2.07
                ],
                [
                    "2025-05-26",
                    5.56
                ],
                [
                    "2025-05-27",
                    11.32
                ],
                [
                    "2025-05-28",
                    6.51
                ],
                [
                    "2025-05-29",
                    8.88
                ],
                [
                    "2025-05-30",
                    -0.4
                ],
                [
                    "2025-06-03",
                    -5.47
                ],
                [
                    "2025-06-04",
                    8.0
                ],
                [
                    "2025-06-05",
                    6.47
                ],
                [
                    "2025-06-06",
                    1.16
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2024-12-27",
                    8.37
                ],
                [
                    "2024-12-30",
                    -1.46
                ],
                [
                    "2024-12-31",
                    -4.9
                ],
                [
                    "2025-01-02",
                    3.75
                ],
                [
                    "2025-01-03",
                    -0.89
                ],
                [
                    "2025-01-08",
                    -0.12
                ],
                [
                    "2025-01-09",
                    7.14
                ],
                [
                    "2025-01-10",
                    3.18
                ],
                [
                    "2025-01-13",
                    1.33
                ],
                [
                    "2025-01-14",
                    -10.37
                ],
                [
                    "2025-03-03",
                    1.52
                ],
                [
                    "2025-03-28",
                    14.17
                ],
                [
                    "2025-03-31",
                    3.06
                ],
                [
                    "2025-04-01",
                    -9.41
                ],
                [
                    "2025-04-03",
                    6.68
                ],
                [
                    "2025-04-16",
                    5.96
                ],
                [
                    "2025-05-07",
                    2.12
                ],
                [
                    "2025-05-22",
                    -3.57
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2024-12-24",
                    4.29
                ],
                [
                    "2024-12-25",
                    7.25
                ],
                [
                    "2024-12-26",
                    -1.44
                ],
                [
                    "2024-12-27",
                    -6.06
                ],
                [
                    "2024-12-30",
                    4.22
                ],
                [
                    "2024-12-31",
                    2.6
                ],
                [
                    "2025-01-03",
                    3.02
                ],
                [
                    "2025-01-06",
                    0.66
                ],
                [
                    "2025-01-07",
                    2.65
                ],
                [
                    "2025-01-08",
                    2.19
                ],
                [
                    "2025-01-09",
                    -2.72
                ],
                [
                    "2025-01-10",
                    -0.3
                ],
                [
                    "2025-01-13",
                    0.23
                ],
                [
                    "2025-01-14",
                    7.37
                ],
                [
                    "2025-01-15",
                    4.06
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2024-12-11",
                    10.0
                ],
                [
                    "2024-12-12",
                    3.75
                ],
                [
                    "2024-12-13",
                    20.26
                ],
                [
                    "2024-12-16",
                    6.24
                ],
                [
                    "2024-12-17",
                    6.33
                ],
                [
                    "2024-12-18",
                    -1.03
                ],
                [
                    "2024-12-19",
                    1.06
                ],
                [
                    "2024-12-20",
                    3.22
                ],
                [
                    "2025-01-16",
                    0.3
                ],
                [
                    "2025-01-17",
                    8.64
                ],
                [
                    "2025-01-20",
                    10.87
                ],
                [
                    "2025-01-21",
                    8.65
                ],
                [
                    "2025-01-22",
                    -6.82
                ],
                [
                    "2025-01-23",
                    4.69
                ],
                [
                    "2025-01-24",
                    6.18
                ],
                [
                    "2025-01-27",
                    2.43
                ],
                [
                    "2025-02-06",
                    4.57
                ],
                [
                    "2025-02-07",
                    3.91
                ],
                [
                    "2025-02-10",
                    -1.8
                ],
                [
                    "2025-02-11",
                    0.36
                ],
                [
                    "2025-02-12",
                    11.47
                ],
                [
                    "2025-02-13",
                    6.49
                ],
                [
                    "2025-02-14",
                    7.99
                ],
                [
                    "2025-02-17",
                    9.31
                ],
                [
                    "2025-02-18",
                    -4.84
                ],
                [
                    "2025-02-19",
                    -0.85
                ],
                [
                    "2025-02-20",
                    8.73
                ],
                [
                    "2025-02-21",
                    10.05
                ],
                [
                    "2025-02-24",
                    -3.22
                ],
                [
                    "2025-02-25",
                    5.04
                ],
                [
                    "2025-02-26",
                    1.38
                ],
                [
                    "2025-03-06",
                    10.04
                ],
                [
                    "2025-03-07",
                    1.18
                ],
                [
                    "2025-03-10",
                    7.29
                ],
                [
                    "2025-03-11",
                    12.97
                ],
                [
                    "2025-03-12",
                    2.87
                ],
                [
                    "2025-03-13",
                    -5.38
                ],
                [
                    "2025-03-14",
                    -6.76
                ],
                [
                    "2025-03-17",
                    11.19
                ],
                [
                    "2025-03-18",
                    3.68
                ],
                [
                    "2025-03-19",
                    -1.76
                ],
                [
                    "2025-03-20",
                    12.79
                ],
                [
                    "2025-03-21",
                    19.09
                ],
                [
                    "2025-03-24",
                    -0.08
                ],
                [
                    "2025-03-25",
                    -1.71
                ],
                [
                    "2025-03-26",
                    -0.19
                ],
                [
                    "2025-03-27",
                    -1.3
                ],
                [
                    "2025-04-07",
                    4.84
                ],
                [
                    "2025-04-08",
                    10.88
                ],
                [
                    "2025-04-09",
                    6.79
                ],
                [
                    "2025-04-17",
                    15.16
                ],
                [
                    "2025-04-18",
                    4.49
                ],
                [
                    "2025-04-21",
                    -6.68
                ],
                [
                    "2025-04-22",
                    7.49
                ],
                [
                    "2025-04-23",
                    9.82
                ],
                [
                    "2025-04-24",
                    -0.52
                ],
                [
                    "2025-04-25",
                    11.59
                ],
                [
                    "2025-04-28",
                    2.82
                ],
                [
                    "2025-04-29",
                    -5.83
                ],
                [
                    "2025-04-30",
                    10.04
                ],
                [
                    "2025-05-06",
                    -5.05
                ],
                [
                    "2025-05-09",
                    8.54
                ],
                [
                    "2025-05-12",
                    14.56
                ],
                [
                    "2025-05-13",
                    -9.05
                ],
                [
                    "2025-05-14",
                    6.93
                ],
                [
                    "2025-05-15",
                    10.99
                ],
                [
                    "2025-05-16",
                    -5.31
                ],
                [
                    "2025-05-20",
                    4.17
                ],
                [
                    "2025-05-26",
                    5.56
                ],
                [
                    "2025-05-27",
                    11.32
                ],
                [
                    "2025-05-28",
                    6.51
                ],
                [
                    "2025-05-29",
                    8.88
                ],
                [
                    "2025-05-30",
                    -0.4
                ],
                [
                    "2025-06-03",
                    -5.47
                ],
                [
                    "2025-06-04",
                    8.0
                ],
                [
                    "2025-06-05",
                    6.47
                ],
                [
                    "2025-06-06",
                    1.16
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-02",
                "2024-12-03",
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "001337 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_f05df84ca83340ba820673dac4e970bb.setOption(option_f05df84ca83340ba820673dac4e970bb);
            window.addEventListener('resize', function(){
                chart_f05df84ca83340ba820673dac4e970bb.resize();
            })
    </script>

<script>
// 等待页面完全加载
window.addEventListener('load', function() {
    setTimeout(function() {
        // 获取所有图表实例
        var charts = [];
        var chartDoms = document.querySelectorAll('[_echarts_instance_]');
        chartDoms.forEach(function(dom) {
            var chart = echarts.getInstanceByDom(dom);
            if (chart) {
                charts.push(chart);
            }
        });

        console.log('找到图表数量:', charts.length);

        if (charts.length >= 2) {
            // 确保缩放联动
            echarts.connect(charts);
            console.log('缩放联动已设置');

            var chart1 = charts[0];
            var chart2 = charts[1];

            // 简单的鼠标悬停联动
            var isUpdating = false;

            chart1.on('mousemove', function(params) {
                if (!isUpdating && params.dataIndex !== undefined) {
                    isUpdating = true;
                    chart2.dispatchAction({
                        type: 'showTip',
                        seriesIndex: 0,
                        dataIndex: params.dataIndex
                    });
                    setTimeout(function() { isUpdating = false; }, 10);
                }
            });

            chart2.on('mousemove', function(params) {
                if (!isUpdating && params.dataIndex !== undefined) {
                    isUpdating = true;
                    chart1.dispatchAction({
                        type: 'showTip',
                        seriesIndex: 0,
                        dataIndex: params.dataIndex
                    });
                    setTimeout(function() { isUpdating = false; }, 10);
                }
            });

            console.log('鼠标悬停联动已设置');
        }
    }, 2000);
});
</script>

</body>
</html>
