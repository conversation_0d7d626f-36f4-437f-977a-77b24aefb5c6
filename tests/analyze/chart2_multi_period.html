<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="27a7278502664bfd95b7d26bdf2d455b" class="chart-container" style="width:1400px; height:700px; "></div>
    <script>
        var chart_27a7278502664bfd95b7d26bdf2d455b = echarts.init(
            document.getElementById('27a7278502664bfd95b7d26bdf2d455b'), 'white', {renderer: 'canvas'});
        var option_27a7278502664bfd95b7d26bdf2d455b = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "bar",
            "name": "5\u65e5",
            "legendHoverLink": true,
            "data": [
                0,
                11.27,
                17.08,
                10.09,
                39.58,
                8.09,
                0,
                0,
                0,
                0,
                10.22,
                10.83,
                0,
                0,
                0,
                23.21,
                0,
                0,
                17.45,
                20.53,
                0,
                14.91,
                10.14,
                21.96,
                18.68,
                0,
                9.45
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#ff6b6b"
            }
        },
        {
            "type": "bar",
            "name": "10\u65e5",
            "legendHoverLink": true,
            "data": [
                0,
                0,
                15.27,
                0,
                0,
                0,
                7.93,
                2.54,
                2.43,
                0,
                6.35,
                0,
                2.42,
                1.91,
                0,
                20.49,
                2.62,
                0,
                17.55,
                0,
                0,
                4.43,
                0,
                4.98,
                15.56,
                4.25,
                3.81
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#4ecdc4"
            }
        },
        {
            "type": "bar",
            "name": "15\u65e5",
            "legendHoverLink": true,
            "data": [
                -4.14,
                0,
                16.43,
                0,
                0,
                0,
                3.87,
                0,
                2.29,
                -4.06,
                2.9,
                0,
                0.2,
                -1.63,
                -1.84,
                0,
                -4.42,
                1.16,
                0,
                0,
                -0.52,
                0,
                0,
                1.12,
                0,
                -1.61,
                1.92
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#45b7d1"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "5\u65e5",
                "10\u65e5",
                "15\u65e5"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "orient": "horizontal",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "shadow"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "name": "\u884c\u4e1a",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "\u4e13\u4e1a\u670d\u52a1",
                "\u4e92\u8054\u7f51\u670d\u52a1",
                "\u4fdd\u9669",
                "\u5316\u5b66\u539f\u6599",
                "\u534a\u5bfc\u4f53",
                "\u5c0f\u91d1\u5c5e",
                "\u5de5\u7a0b\u54a8\u8be2\u670d\u52a1",
                "\u5de5\u7a0b\u5efa\u8bbe",
                "\u623f\u5730\u4ea7\u670d\u52a1",
                "\u6559\u80b2",
                "\u65c5\u6e38\u9152\u5e97",
                "\u6709\u8272\u91d1\u5c5e",
                "\u6a61\u80f6\u5236\u54c1",
                "\u6c34\u6ce5\u5efa\u6750",
                "\u6c7d\u8f66\u670d\u52a1",
                "\u6d88\u8d39\u7535\u5b50",
                "\u73bb\u7483\u73bb\u7ea4",
                "\u73e0\u5b9d\u9996\u9970",
                "\u7535\u5b50\u5143\u4ef6",
                "\u7535\u7f51\u8bbe\u5907",
                "\u88c5\u4fee\u5efa\u6750",
                "\u8bc1\u5238",
                "\u8d35\u91d1\u5c5e",
                "\u901a\u4fe1\u670d\u52a1",
                "\u901a\u4fe1\u8bbe\u5907",
                "\u94c1\u8def\u516c\u8def",
                "\u975e\u91d1\u5c5e\u6750\u6599"
            ]
        }
    ],
    "yAxis": [
        {
            "name": "\u4e3b\u529b\u8d44\u91d1\u6d41\u5165 (\u4ebf\u5143)",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u8fd15\u65e5\u300110\u65e5\u300115\u65e5\u4e3b\u529b\u8d44\u91d1\u6d41\u5165\u5bf9\u6bd4 (\u524d15\u884c\u4e1a)",
            "target": "blank",
            "subtext": "\u5355\u4f4d\uff1a\u4ebf\u5143",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_27a7278502664bfd95b7d26bdf2d455b.setOption(option_27a7278502664bfd95b7d26bdf2d455b);
    </script>
</body>
</html>
