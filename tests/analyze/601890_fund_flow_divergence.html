<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="850b50b22e0442f8a0a6a0376acbfc59" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_850b50b22e0442f8a0a6a0376acbfc59 = echarts.init(
            document.getElementById('850b50b22e0442f8a0a6a0376acbfc59'), 'white', {renderer: 'canvas'});
            
    // 等待页面加载完成
    setTimeout(function() {
        // 获取所有图表实例
        var charts = [];
        var chartDoms = document.querySelectorAll('[_echarts_instance_]');
        chartDoms.forEach(function(dom) {
            var chart = echarts.getInstanceByDom(dom);
            if (chart) {
                charts.push(chart);
            }
        });

        console.log('找到图表数量:', charts.length);

        // 设置缩放联动
        if (charts.length >= 2) {
            echarts.connect(charts);
            console.log('图表缩放联动已设置');
        }
    }, 100);

        var option_850b50b22e0442f8a0a6a0376acbfc59 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    8.08,
                    8.78,
                    8.08,
                    8.78
                ],
                [
                    8.8,
                    8.72,
                    8.57,
                    9.08
                ],
                [
                    8.58,
                    8.45,
                    8.38,
                    8.65
                ],
                [
                    8.43,
                    8.49,
                    8.35,
                    8.59
                ],
                [
                    8.48,
                    8.47,
                    8.35,
                    8.51
                ],
                [
                    8.44,
                    8.47,
                    8.37,
                    8.58
                ],
                [
                    8.61,
                    8.42,
                    8.4,
                    8.68
                ],
                [
                    8.48,
                    8.81,
                    8.45,
                    8.88
                ],
                [
                    8.8,
                    8.72,
                    8.64,
                    8.88
                ],
                [
                    8.71,
                    8.54,
                    8.53,
                    8.78
                ],
                [
                    8.53,
                    8.46,
                    8.4,
                    8.6
                ],
                [
                    8.43,
                    8.23,
                    8.2,
                    8.54
                ],
                [
                    8.24,
                    8.23,
                    8.17,
                    8.36
                ],
                [
                    8.14,
                    8.23,
                    8.06,
                    8.27
                ],
                [
                    8.3,
                    8.21,
                    8.16,
                    8.32
                ],
                [
                    8.22,
                    8.01,
                    7.97,
                    8.23
                ],
                [
                    7.99,
                    8.05,
                    7.99,
                    8.12
                ],
                [
                    8.01,
                    7.87,
                    7.81,
                    8.08
                ],
                [
                    7.88,
                    7.92,
                    7.86,
                    7.97
                ],
                [
                    7.95,
                    8.08,
                    7.95,
                    8.12
                ],
                [
                    8.07,
                    8.02,
                    7.97,
                    8.17
                ],
                [
                    8.03,
                    7.79,
                    7.77,
                    8.03
                ],
                [
                    7.71,
                    7.5,
                    7.42,
                    7.79
                ],
                [
                    7.61,
                    7.64,
                    7.55,
                    8.08
                ],
                [
                    7.4,
                    7.34,
                    7.27,
                    7.53
                ],
                [
                    7.32,
                    7.42,
                    7.28,
                    7.43
                ],
                [
                    7.39,
                    7.41,
                    7.21,
                    7.48
                ],
                [
                    7.33,
                    7.38,
                    7.33,
                    7.47
                ],
                [
                    7.36,
                    7.27,
                    7.27,
                    7.42
                ],
                [
                    7.22,
                    7.32,
                    7.19,
                    7.38
                ],
                [
                    7.48,
                    7.9,
                    7.36,
                    7.93
                ],
                [
                    7.93,
                    7.84,
                    7.74,
                    7.94
                ],
                [
                    7.89,
                    7.89,
                    7.8,
                    7.96
                ],
                [
                    8.19,
                    7.83,
                    7.81,
                    8.68
                ],
                [
                    7.87,
                    7.81,
                    7.72,
                    7.93
                ],
                [
                    7.85,
                    7.83,
                    7.7,
                    7.87
                ],
                [
                    7.81,
                    7.74,
                    7.7,
                    7.83
                ],
                [
                    7.81,
                    7.68,
                    7.67,
                    7.87
                ],
                [
                    7.63,
                    7.73,
                    7.61,
                    7.74
                ],
                [
                    7.76,
                    7.64,
                    7.6,
                    7.79
                ],
                [
                    7.65,
                    7.69,
                    7.63,
                    7.72
                ],
                [
                    7.66,
                    7.85,
                    7.6,
                    7.85
                ],
                [
                    7.87,
                    7.85,
                    7.77,
                    7.9
                ],
                [
                    7.85,
                    7.86,
                    7.78,
                    7.88
                ],
                [
                    7.86,
                    7.83,
                    7.78,
                    7.87
                ],
                [
                    7.8,
                    7.85,
                    7.78,
                    7.86
                ],
                [
                    7.86,
                    7.76,
                    7.76,
                    7.88
                ],
                [
                    7.77,
                    7.74,
                    7.71,
                    7.8
                ],
                [
                    7.74,
                    7.74,
                    7.66,
                    7.78
                ],
                [
                    7.7,
                    7.51,
                    7.47,
                    7.74
                ],
                [
                    7.48,
                    7.53,
                    7.48,
                    7.57
                ],
                [
                    7.52,
                    7.5,
                    7.46,
                    7.54
                ],
                [
                    7.52,
                    7.53,
                    7.45,
                    7.53
                ],
                [
                    7.54,
                    7.56,
                    7.49,
                    7.61
                ],
                [
                    7.52,
                    7.54,
                    7.46,
                    7.61
                ],
                [
                    7.54,
                    7.63,
                    7.53,
                    7.65
                ],
                [
                    7.63,
                    7.58,
                    7.48,
                    7.66
                ],
                [
                    7.53,
                    7.47,
                    7.44,
                    7.6
                ],
                [
                    7.46,
                    7.42,
                    7.41,
                    7.54
                ],
                [
                    7.43,
                    7.54,
                    7.41,
                    7.56
                ],
                [
                    7.5,
                    7.46,
                    7.42,
                    7.53
                ],
                [
                    7.48,
                    7.53,
                    7.44,
                    7.54
                ],
                [
                    7.52,
                    7.54,
                    7.47,
                    7.65
                ],
                [
                    7.54,
                    7.71,
                    7.51,
                    7.74
                ],
                [
                    7.65,
                    7.78,
                    7.61,
                    7.78
                ],
                [
                    7.78,
                    7.86,
                    7.71,
                    7.97
                ],
                [
                    7.87,
                    8.25,
                    7.71,
                    8.35
                ],
                [
                    8.25,
                    8.46,
                    8.17,
                    8.65
                ],
                [
                    8.76,
                    8.36,
                    8.32,
                    9.05
                ],
                [
                    8.3,
                    8.81,
                    8.3,
                    9.02
                ],
                [
                    8.6,
                    8.58,
                    8.5,
                    8.86
                ],
                [
                    8.59,
                    9.44,
                    8.5,
                    9.44
                ],
                [
                    9.89,
                    10.38,
                    9.7,
                    10.38
                ],
                [
                    11.1,
                    11.42,
                    10.54,
                    11.42
                ],
                [
                    10.58,
                    10.28,
                    10.28,
                    10.98
                ],
                [
                    9.58,
                    10.02,
                    9.58,
                    10.62
                ],
                [
                    9.72,
                    10.58,
                    9.18,
                    10.93
                ],
                [
                    10.1,
                    10.31,
                    9.66,
                    10.76
                ],
                [
                    10.0,
                    9.6,
                    9.3,
                    10.11
                ],
                [
                    9.53,
                    9.29,
                    9.28,
                    9.8
                ],
                [
                    9.14,
                    9.2,
                    9.04,
                    9.42
                ],
                [
                    9.01,
                    8.87,
                    8.71,
                    9.18
                ],
                [
                    8.3,
                    7.98,
                    7.98,
                    8.49
                ],
                [
                    7.88,
                    8.08,
                    7.88,
                    8.24
                ],
                [
                    7.88,
                    8.7,
                    7.57,
                    8.86
                ],
                [
                    8.6,
                    8.71,
                    8.57,
                    8.92
                ],
                [
                    8.6,
                    8.68,
                    8.57,
                    8.82
                ],
                [
                    8.7,
                    8.87,
                    8.7,
                    8.98
                ],
                [
                    8.87,
                    8.7,
                    8.6,
                    8.9
                ],
                [
                    8.65,
                    8.53,
                    8.42,
                    8.9
                ],
                [
                    8.45,
                    8.61,
                    8.45,
                    8.85
                ],
                [
                    8.55,
                    9.02,
                    8.55,
                    9.13
                ],
                [
                    8.93,
                    9.04,
                    8.73,
                    9.1
                ],
                [
                    8.97,
                    8.74,
                    8.73,
                    8.97
                ],
                [
                    8.83,
                    8.82,
                    8.69,
                    8.9
                ],
                [
                    8.8,
                    8.61,
                    8.57,
                    8.8
                ],
                [
                    8.59,
                    8.59,
                    8.55,
                    8.69
                ],
                [
                    8.55,
                    8.49,
                    8.46,
                    8.65
                ],
                [
                    8.49,
                    8.55,
                    8.39,
                    8.6
                ],
                [
                    8.54,
                    8.6,
                    8.53,
                    8.65
                ],
                [
                    8.65,
                    8.89,
                    8.64,
                    8.89
                ],
                [
                    9.0,
                    9.3,
                    8.89,
                    9.42
                ],
                [
                    9.13,
                    9.29,
                    8.98,
                    9.35
                ],
                [
                    9.34,
                    9.1,
                    9.04,
                    9.4
                ],
                [
                    9.08,
                    9.34,
                    9.0,
                    9.6
                ],
                [
                    9.43,
                    9.03,
                    9.01,
                    9.43
                ],
                [
                    9.02,
                    9.04,
                    8.96,
                    9.09
                ],
                [
                    8.98,
                    8.75,
                    8.75,
                    9.01
                ],
                [
                    8.82,
                    8.83,
                    8.78,
                    9.2
                ],
                [
                    8.83,
                    8.84,
                    8.69,
                    8.88
                ],
                [
                    8.85,
                    8.92,
                    8.75,
                    8.95
                ],
                [
                    8.86,
                    8.74,
                    8.72,
                    8.88
                ],
                [
                    8.72,
                    8.52,
                    8.51,
                    8.72
                ],
                [
                    8.51,
                    8.46,
                    8.45,
                    8.62
                ],
                [
                    8.46,
                    8.54,
                    8.45,
                    8.55
                ],
                [
                    8.52,
                    8.42,
                    8.38,
                    8.53
                ],
                [
                    8.43,
                    8.77,
                    8.34,
                    8.85
                ],
                [
                    8.62,
                    8.73,
                    8.56,
                    8.8
                ],
                [
                    8.66,
                    8.64,
                    8.52,
                    8.7
                ],
                [
                    8.58,
                    8.73,
                    8.5,
                    8.78
                ],
                [
                    8.73,
                    9.06,
                    8.68,
                    9.06
                ],
                [
                    8.99,
                    8.88,
                    8.85,
                    9.06
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-04",
                    -13.87
                ],
                [
                    "2024-12-05",
                    2.04
                ],
                [
                    "2024-12-06",
                    -4.19
                ],
                [
                    "2024-12-09",
                    3.23
                ],
                [
                    "2024-12-10",
                    -4.39
                ],
                [
                    "2024-12-11",
                    14.34
                ],
                [
                    "2024-12-12",
                    -10.0
                ],
                [
                    "2024-12-13",
                    -3.66
                ],
                [
                    "2024-12-16",
                    -11.5
                ],
                [
                    "2024-12-17",
                    -16.32
                ],
                [
                    "2024-12-18",
                    3.4
                ],
                [
                    "2024-12-19",
                    -7.22
                ],
                [
                    "2024-12-20",
                    3.43
                ],
                [
                    "2024-12-23",
                    -5.02
                ],
                [
                    "2024-12-24",
                    -6.89
                ],
                [
                    "2024-12-25",
                    -12.56
                ],
                [
                    "2024-12-26",
                    -8.23
                ],
                [
                    "2024-12-27",
                    0.1
                ],
                [
                    "2024-12-30",
                    3.21
                ],
                [
                    "2024-12-31",
                    -5.99
                ],
                [
                    "2025-01-02",
                    -9.23
                ],
                [
                    "2025-01-03",
                    7.54
                ],
                [
                    "2025-01-06",
                    -11.82
                ],
                [
                    "2025-01-07",
                    -3.6
                ],
                [
                    "2025-01-08",
                    -5.34
                ],
                [
                    "2025-01-09",
                    -7.62
                ],
                [
                    "2025-01-10",
                    -6.02
                ],
                [
                    "2025-01-13",
                    -3.77
                ],
                [
                    "2025-01-14",
                    1.95
                ],
                [
                    "2025-01-15",
                    0.68
                ],
                [
                    "2025-01-16",
                    5.69
                ],
                [
                    "2025-01-17",
                    7.66
                ],
                [
                    "2025-01-20",
                    -12.23
                ],
                [
                    "2025-01-21",
                    -2.08
                ],
                [
                    "2025-01-22",
                    -11.23
                ],
                [
                    "2025-01-23",
                    -7.29
                ],
                [
                    "2025-01-24",
                    -8.3
                ],
                [
                    "2025-01-27",
                    -10.18
                ],
                [
                    "2025-02-05",
                    -8.32
                ],
                [
                    "2025-02-06",
                    7.7
                ],
                [
                    "2025-02-07",
                    0.87
                ],
                [
                    "2025-02-10",
                    -9.74
                ],
                [
                    "2025-02-11",
                    -5.68
                ],
                [
                    "2025-02-12",
                    -4.54
                ],
                [
                    "2025-02-13",
                    1.76
                ],
                [
                    "2025-02-14",
                    -16.08
                ],
                [
                    "2025-02-17",
                    -2.01
                ],
                [
                    "2025-02-18",
                    -21.41
                ],
                [
                    "2025-02-19",
                    -15.86
                ],
                [
                    "2025-02-20",
                    -7.89
                ],
                [
                    "2025-02-21",
                    -5.24
                ],
                [
                    "2025-02-24",
                    -0.03
                ],
                [
                    "2025-02-25",
                    -6.52
                ],
                [
                    "2025-02-26",
                    -2.62
                ],
                [
                    "2025-02-27",
                    2.44
                ],
                [
                    "2025-02-28",
                    -7.94
                ],
                [
                    "2025-03-03",
                    -4.2
                ],
                [
                    "2025-03-04",
                    -5.91
                ],
                [
                    "2025-03-05",
                    -5.74
                ],
                [
                    "2025-03-06",
                    -3.97
                ],
                [
                    "2025-03-07",
                    -1.91
                ],
                [
                    "2025-03-10",
                    13.31
                ],
                [
                    "2025-03-11",
                    -0.84
                ],
                [
                    "2025-03-12",
                    9.14
                ],
                [
                    "2025-03-13",
                    14.32
                ],
                [
                    "2025-03-14",
                    1.92
                ],
                [
                    "2025-03-17",
                    -6.83
                ],
                [
                    "2025-03-18",
                    4.07
                ],
                [
                    "2025-03-19",
                    -7.81
                ],
                [
                    "2025-03-20",
                    24.63
                ],
                [
                    "2025-03-21",
                    1.59
                ],
                [
                    "2025-03-24",
                    -8.27
                ],
                [
                    "2025-03-25",
                    -5.91
                ],
                [
                    "2025-03-26",
                    -5.54
                ],
                [
                    "2025-03-27",
                    3.37
                ],
                [
                    "2025-03-28",
                    -5.62
                ],
                [
                    "2025-03-31",
                    0.77
                ],
                [
                    "2025-04-01",
                    -1.43
                ],
                [
                    "2025-04-02",
                    -0.25
                ],
                [
                    "2025-04-03",
                    -9.27
                ],
                [
                    "2025-04-07",
                    -13.56
                ],
                [
                    "2025-04-08",
                    -5.28
                ],
                [
                    "2025-04-09",
                    2.08
                ],
                [
                    "2025-04-10",
                    -8.6
                ],
                [
                    "2025-04-11",
                    -3.42
                ],
                [
                    "2025-04-14",
                    -0.72
                ],
                [
                    "2025-04-15",
                    -6.84
                ],
                [
                    "2025-04-16",
                    -4.12
                ],
                [
                    "2025-04-17",
                    3.68
                ],
                [
                    "2025-04-18",
                    7.03
                ],
                [
                    "2025-04-21",
                    1.69
                ],
                [
                    "2025-04-22",
                    -20.08
                ],
                [
                    "2025-04-23",
                    0.78
                ],
                [
                    "2025-04-24",
                    -15.59
                ],
                [
                    "2025-04-25",
                    -4.7
                ],
                [
                    "2025-04-28",
                    -6.28
                ],
                [
                    "2025-04-29",
                    -1.17
                ],
                [
                    "2025-04-30",
                    3.2
                ],
                [
                    "2025-05-06",
                    -1.11
                ],
                [
                    "2025-05-07",
                    0.16
                ],
                [
                    "2025-05-08",
                    -0.32
                ],
                [
                    "2025-05-09",
                    -6.16
                ],
                [
                    "2025-05-12",
                    -1.38
                ],
                [
                    "2025-05-13",
                    -20.23
                ],
                [
                    "2025-05-14",
                    -1.89
                ],
                [
                    "2025-05-15",
                    -10.53
                ],
                [
                    "2025-05-16",
                    3.23
                ],
                [
                    "2025-05-19",
                    -5.85
                ],
                [
                    "2025-05-20",
                    6.19
                ],
                [
                    "2025-05-21",
                    -9.07
                ],
                [
                    "2025-05-22",
                    -6.82
                ],
                [
                    "2025-05-23",
                    -3.98
                ],
                [
                    "2025-05-26",
                    -1.27
                ],
                [
                    "2025-05-27",
                    -11.19
                ],
                [
                    "2025-05-28",
                    10.77
                ],
                [
                    "2025-05-29",
                    -7.11
                ],
                [
                    "2025-05-30",
                    0.49
                ],
                [
                    "2025-06-03",
                    10.82
                ],
                [
                    "2025-06-04",
                    5.17
                ],
                [
                    "2025-06-05",
                    -11.56
                ],
                [
                    "2025-06-06",
                    -4.86
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-04",
                    3.46
                ],
                [
                    "2024-12-05",
                    -0.2
                ],
                [
                    "2024-12-06",
                    2.59
                ],
                [
                    "2024-12-09",
                    -1.25
                ],
                [
                    "2024-12-10",
                    2.85
                ],
                [
                    "2024-12-11",
                    -1.8
                ],
                [
                    "2024-12-12",
                    -3.1
                ],
                [
                    "2024-12-13",
                    1.02
                ],
                [
                    "2024-12-16",
                    1.58
                ],
                [
                    "2024-12-17",
                    2.81
                ],
                [
                    "2024-12-18",
                    -2.67
                ],
                [
                    "2024-12-19",
                    0.31
                ],
                [
                    "2024-12-20",
                    2.67
                ],
                [
                    "2024-12-23",
                    -1.35
                ],
                [
                    "2024-12-24",
                    2.33
                ],
                [
                    "2024-12-25",
                    -0.1
                ],
                [
                    "2024-12-26",
                    6.01
                ],
                [
                    "2024-12-27",
                    -3.5
                ],
                [
                    "2024-12-30",
                    0.45
                ],
                [
                    "2024-12-31",
                    2.22
                ],
                [
                    "2025-01-02",
                    1.3
                ],
                [
                    "2025-01-03",
                    2.91
                ],
                [
                    "2025-01-06",
                    0.18
                ],
                [
                    "2025-01-07",
                    5.69
                ],
                [
                    "2025-01-08",
                    2.32
                ],
                [
                    "2025-01-09",
                    4.16
                ],
                [
                    "2025-01-10",
                    -1.76
                ],
                [
                    "2025-01-13",
                    -5.07
                ],
                [
                    "2025-01-14",
                    -5.13
                ],
                [
                    "2025-01-15",
                    -4.88
                ],
                [
                    "2025-01-16",
                    -5.61
                ],
                [
                    "2025-01-17",
                    -0.69
                ],
                [
                    "2025-01-20",
                    -1.71
                ],
                [
                    "2025-01-21",
                    -2.84
                ],
                [
                    "2025-01-22",
                    1.04
                ],
                [
                    "2025-01-23",
                    2.71
                ],
                [
                    "2025-01-24",
                    -1.19
                ],
                [
                    "2025-01-27",
                    7.87
                ],
                [
                    "2025-02-05",
                    2.01
                ],
                [
                    "2025-02-06",
                    -3.8
                ],
                [
                    "2025-02-07",
                    0.39
                ],
                [
                    "2025-02-10",
                    4.91
                ],
                [
                    "2025-02-11",
                    -0.76
                ],
                [
                    "2025-02-12",
                    1.06
                ],
                [
                    "2025-02-13",
                    -0.55
                ],
                [
                    "2025-02-14",
                    8.42
                ],
                [
                    "2025-02-17",
                    0.34
                ],
                [
                    "2025-02-18",
                    1.9
                ],
                [
                    "2025-02-19",
                    2.93
                ],
                [
                    "2025-02-20",
                    -1.59
                ],
                [
                    "2025-02-21",
                    -2.9
                ],
                [
                    "2025-02-24",
                    0.24
                ],
                [
                    "2025-02-25",
                    -3.03
                ],
                [
                    "2025-02-26",
                    1.18
                ],
                [
                    "2025-02-27",
                    0.08
                ],
                [
                    "2025-02-28",
                    -0.04
                ],
                [
                    "2025-03-03",
                    7.76
                ],
                [
                    "2025-03-04",
                    4.0
                ],
                [
                    "2025-03-05",
                    2.48
                ],
                [
                    "2025-03-06",
                    -0.2
                ],
                [
                    "2025-03-07",
                    2.59
                ],
                [
                    "2025-03-10",
                    -4.93
                ],
                [
                    "2025-03-11",
                    4.1
                ],
                [
                    "2025-03-12",
                    -3.28
                ],
                [
                    "2025-03-13",
                    -6.15
                ],
                [
                    "2025-03-14",
                    -0.82
                ],
                [
                    "2025-03-17",
                    -0.51
                ],
                [
                    "2025-03-18",
                    -3.4
                ],
                [
                    "2025-03-19",
                    1.1
                ],
                [
                    "2025-03-20",
                    -12.88
                ],
                [
                    "2025-03-21",
                    -4.16
                ],
                [
                    "2025-03-24",
                    1.46
                ],
                [
                    "2025-03-25",
                    1.44
                ],
                [
                    "2025-03-26",
                    1.77
                ],
                [
                    "2025-03-27",
                    -0.63
                ],
                [
                    "2025-03-28",
                    4.08
                ],
                [
                    "2025-03-31",
                    -1.79
                ],
                [
                    "2025-04-01",
                    -2.57
                ],
                [
                    "2025-04-02",
                    -0.58
                ],
                [
                    "2025-04-03",
                    -4.92
                ],
                [
                    "2025-04-07",
                    3.03
                ],
                [
                    "2025-04-08",
                    -0.57
                ],
                [
                    "2025-04-09",
                    -0.66
                ],
                [
                    "2025-04-10",
                    0.64
                ],
                [
                    "2025-04-11",
                    -4.67
                ],
                [
                    "2025-04-14",
                    -1.1
                ],
                [
                    "2025-04-15",
                    -9.3
                ],
                [
                    "2025-04-16",
                    -0.86
                ],
                [
                    "2025-04-17",
                    -2.83
                ],
                [
                    "2025-04-18",
                    3.77
                ],
                [
                    "2025-04-21",
                    2.75
                ],
                [
                    "2025-04-22",
                    3.14
                ],
                [
                    "2025-04-23",
                    -2.46
                ],
                [
                    "2025-04-24",
                    -0.95
                ],
                [
                    "2025-04-25",
                    -2.82
                ],
                [
                    "2025-04-28",
                    -0.01
                ],
                [
                    "2025-04-29",
                    4.19
                ],
                [
                    "2025-04-30",
                    -0.19
                ],
                [
                    "2025-05-06",
                    -4.8
                ],
                [
                    "2025-05-07",
                    3.51
                ],
                [
                    "2025-05-08",
                    2.05
                ],
                [
                    "2025-05-09",
                    -3.78
                ],
                [
                    "2025-05-12",
                    0.39
                ],
                [
                    "2025-05-13",
                    7.5
                ],
                [
                    "2025-05-14",
                    -3.26
                ],
                [
                    "2025-05-15",
                    -0.56
                ],
                [
                    "2025-05-16",
                    2.14
                ],
                [
                    "2025-05-19",
                    2.12
                ],
                [
                    "2025-05-20",
                    1.61
                ],
                [
                    "2025-05-21",
                    0.19
                ],
                [
                    "2025-05-22",
                    -8.39
                ],
                [
                    "2025-05-23",
                    1.01
                ],
                [
                    "2025-05-26",
                    4.62
                ],
                [
                    "2025-05-27",
                    -2.63
                ],
                [
                    "2025-05-28",
                    1.74
                ],
                [
                    "2025-05-29",
                    2.61
                ],
                [
                    "2025-05-30",
                    0.21
                ],
                [
                    "2025-06-03",
                    -5.17
                ],
                [
                    "2025-06-04",
                    0.17
                ],
                [
                    "2025-06-05",
                    3.52
                ],
                [
                    "2025-06-06",
                    -1.82
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-04",
                    10.42
                ],
                [
                    "2024-12-05",
                    -1.84
                ],
                [
                    "2024-12-06",
                    1.6
                ],
                [
                    "2024-12-09",
                    -1.98
                ],
                [
                    "2024-12-10",
                    1.54
                ],
                [
                    "2024-12-11",
                    -12.54
                ],
                [
                    "2024-12-12",
                    13.1
                ],
                [
                    "2024-12-13",
                    2.64
                ],
                [
                    "2024-12-16",
                    9.92
                ],
                [
                    "2024-12-17",
                    13.51
                ],
                [
                    "2024-12-18",
                    -0.72
                ],
                [
                    "2024-12-19",
                    6.91
                ],
                [
                    "2024-12-20",
                    -6.1
                ],
                [
                    "2024-12-23",
                    6.36
                ],
                [
                    "2024-12-24",
                    4.55
                ],
                [
                    "2024-12-25",
                    12.65
                ],
                [
                    "2024-12-26",
                    2.22
                ],
                [
                    "2024-12-27",
                    3.4
                ],
                [
                    "2024-12-30",
                    -3.67
                ],
                [
                    "2024-12-31",
                    3.78
                ],
                [
                    "2025-01-02",
                    7.93
                ],
                [
                    "2025-01-03",
                    -10.45
                ],
                [
                    "2025-01-06",
                    11.64
                ],
                [
                    "2025-01-07",
                    -2.09
                ],
                [
                    "2025-01-08",
                    3.03
                ],
                [
                    "2025-01-09",
                    3.47
                ],
                [
                    "2025-01-10",
                    7.77
                ],
                [
                    "2025-01-13",
                    8.85
                ],
                [
                    "2025-01-14",
                    3.18
                ],
                [
                    "2025-01-15",
                    4.19
                ],
                [
                    "2025-01-16",
                    -0.08
                ],
                [
                    "2025-01-17",
                    -6.97
                ],
                [
                    "2025-01-20",
                    13.94
                ],
                [
                    "2025-01-21",
                    4.92
                ],
                [
                    "2025-01-22",
                    10.19
                ],
                [
                    "2025-01-23",
                    4.57
                ],
                [
                    "2025-01-24",
                    9.49
                ],
                [
                    "2025-01-27",
                    2.32
                ],
                [
                    "2025-02-05",
                    6.31
                ],
                [
                    "2025-02-06",
                    -3.9
                ],
                [
                    "2025-02-07",
                    -1.26
                ],
                [
                    "2025-02-10",
                    4.84
                ],
                [
                    "2025-02-11",
                    6.44
                ],
                [
                    "2025-02-12",
                    3.47
                ],
                [
                    "2025-02-13",
                    -1.21
                ],
                [
                    "2025-02-14",
                    7.66
                ],
                [
                    "2025-02-17",
                    1.67
                ],
                [
                    "2025-02-18",
                    19.5
                ],
                [
                    "2025-02-19",
                    12.92
                ],
                [
                    "2025-02-20",
                    9.48
                ],
                [
                    "2025-02-21",
                    8.14
                ],
                [
                    "2025-02-24",
                    -0.22
                ],
                [
                    "2025-02-25",
                    9.55
                ],
                [
                    "2025-02-26",
                    1.44
                ],
                [
                    "2025-02-27",
                    -2.52
                ],
                [
                    "2025-02-28",
                    7.99
                ],
                [
                    "2025-03-03",
                    -3.56
                ],
                [
                    "2025-03-04",
                    1.9
                ],
                [
                    "2025-03-05",
                    3.27
                ],
                [
                    "2025-03-06",
                    4.17
                ],
                [
                    "2025-03-07",
                    -0.69
                ],
                [
                    "2025-03-10",
                    -8.37
                ],
                [
                    "2025-03-11",
                    -3.25
                ],
                [
                    "2025-03-12",
                    -5.86
                ],
                [
                    "2025-03-13",
                    -8.17
                ],
                [
                    "2025-03-14",
                    -1.1
                ],
                [
                    "2025-03-17",
                    7.34
                ],
                [
                    "2025-03-18",
                    -0.67
                ],
                [
                    "2025-03-19",
                    6.71
                ],
                [
                    "2025-03-20",
                    -11.76
                ],
                [
                    "2025-03-21",
                    2.58
                ],
                [
                    "2025-03-24",
                    6.82
                ],
                [
                    "2025-03-25",
                    4.47
                ],
                [
                    "2025-03-26",
                    3.76
                ],
                [
                    "2025-03-27",
                    -2.74
                ],
                [
                    "2025-03-28",
                    1.54
                ],
                [
                    "2025-03-31",
                    1.02
                ],
                [
                    "2025-04-01",
                    4.0
                ],
                [
                    "2025-04-02",
                    0.83
                ],
                [
                    "2025-04-03",
                    14.19
                ],
                [
                    "2025-04-07",
                    10.52
                ],
                [
                    "2025-04-08",
                    5.85
                ],
                [
                    "2025-04-09",
                    -1.42
                ],
                [
                    "2025-04-10",
                    7.96
                ],
                [
                    "2025-04-11",
                    8.09
                ],
                [
                    "2025-04-14",
                    1.83
                ],
                [
                    "2025-04-15",
                    16.14
                ],
                [
                    "2025-04-16",
                    4.97
                ],
                [
                    "2025-04-17",
                    -0.84
                ],
                [
                    "2025-04-18",
                    -10.8
                ],
                [
                    "2025-04-21",
                    -4.44
                ],
                [
                    "2025-04-22",
                    16.94
                ],
                [
                    "2025-04-23",
                    1.68
                ],
                [
                    "2025-04-24",
                    16.53
                ],
                [
                    "2025-04-25",
                    7.52
                ],
                [
                    "2025-04-28",
                    6.29
                ],
                [
                    "2025-04-29",
                    -3.02
                ],
                [
                    "2025-04-30",
                    -3.02
                ],
                [
                    "2025-05-06",
                    5.91
                ],
                [
                    "2025-05-07",
                    -3.67
                ],
                [
                    "2025-05-08",
                    -1.73
                ],
                [
                    "2025-05-09",
                    9.94
                ],
                [
                    "2025-05-12",
                    0.99
                ],
                [
                    "2025-05-13",
                    12.73
                ],
                [
                    "2025-05-14",
                    5.15
                ],
                [
                    "2025-05-15",
                    11.09
                ],
                [
                    "2025-05-16",
                    -5.36
                ],
                [
                    "2025-05-19",
                    3.73
                ],
                [
                    "2025-05-20",
                    -7.8
                ],
                [
                    "2025-05-21",
                    8.87
                ],
                [
                    "2025-05-22",
                    15.21
                ],
                [
                    "2025-05-23",
                    2.97
                ],
                [
                    "2025-05-26",
                    -3.34
                ],
                [
                    "2025-05-27",
                    13.82
                ],
                [
                    "2025-05-28",
                    -12.51
                ],
                [
                    "2025-05-29",
                    4.49
                ],
                [
                    "2025-05-30",
                    -0.7
                ],
                [
                    "2025-06-03",
                    -5.64
                ],
                [
                    "2025-06-04",
                    -5.33
                ],
                [
                    "2025-06-05",
                    8.04
                ],
                [
                    "2025-06-06",
                    6.68
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2024-12-11",
                    14.34
                ],
                [
                    "2025-03-11",
                    -0.84
                ],
                [
                    "2025-03-12",
                    9.14
                ],
                [
                    "2025-03-13",
                    14.32
                ],
                [
                    "2025-03-14",
                    1.92
                ],
                [
                    "2025-03-17",
                    -6.83
                ],
                [
                    "2025-03-18",
                    4.07
                ],
                [
                    "2025-05-08",
                    -0.32
                ],
                [
                    "2025-06-03",
                    10.82
                ],
                [
                    "2025-06-04",
                    5.17
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2024-12-11",
                    -1.8
                ],
                [
                    "2025-03-11",
                    4.1
                ],
                [
                    "2025-05-08",
                    2.05
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2024-12-10",
                    1.54
                ],
                [
                    "2024-12-12",
                    13.1
                ],
                [
                    "2024-12-13",
                    2.64
                ],
                [
                    "2024-12-16",
                    9.92
                ],
                [
                    "2024-12-17",
                    13.51
                ],
                [
                    "2024-12-18",
                    -0.72
                ],
                [
                    "2024-12-19",
                    6.91
                ],
                [
                    "2024-12-20",
                    -6.1
                ],
                [
                    "2024-12-23",
                    6.36
                ],
                [
                    "2024-12-24",
                    4.55
                ],
                [
                    "2024-12-25",
                    12.65
                ],
                [
                    "2024-12-26",
                    2.22
                ],
                [
                    "2024-12-27",
                    3.4
                ],
                [
                    "2024-12-30",
                    -3.67
                ],
                [
                    "2024-12-31",
                    3.78
                ],
                [
                    "2025-01-02",
                    7.93
                ],
                [
                    "2025-01-03",
                    -10.45
                ],
                [
                    "2025-01-06",
                    11.64
                ],
                [
                    "2025-01-07",
                    -2.09
                ],
                [
                    "2025-01-08",
                    3.03
                ],
                [
                    "2025-01-09",
                    3.47
                ],
                [
                    "2025-01-10",
                    7.77
                ],
                [
                    "2025-01-13",
                    8.85
                ],
                [
                    "2025-01-14",
                    3.18
                ],
                [
                    "2025-01-15",
                    4.19
                ],
                [
                    "2025-01-16",
                    -0.08
                ],
                [
                    "2025-01-21",
                    4.92
                ],
                [
                    "2025-01-22",
                    10.19
                ],
                [
                    "2025-01-23",
                    4.57
                ],
                [
                    "2025-01-24",
                    9.49
                ],
                [
                    "2025-01-27",
                    2.32
                ],
                [
                    "2025-02-05",
                    6.31
                ],
                [
                    "2025-02-06",
                    -3.9
                ],
                [
                    "2025-02-07",
                    -1.26
                ],
                [
                    "2025-02-10",
                    4.84
                ],
                [
                    "2025-02-11",
                    6.44
                ],
                [
                    "2025-02-12",
                    3.47
                ],
                [
                    "2025-02-13",
                    -1.21
                ],
                [
                    "2025-02-14",
                    7.66
                ],
                [
                    "2025-02-17",
                    1.67
                ],
                [
                    "2025-02-18",
                    19.5
                ],
                [
                    "2025-02-19",
                    12.92
                ],
                [
                    "2025-02-20",
                    9.48
                ],
                [
                    "2025-02-21",
                    8.14
                ],
                [
                    "2025-02-24",
                    -0.22
                ],
                [
                    "2025-02-25",
                    9.55
                ],
                [
                    "2025-02-26",
                    1.44
                ],
                [
                    "2025-02-27",
                    -2.52
                ],
                [
                    "2025-02-28",
                    7.99
                ],
                [
                    "2025-03-03",
                    -3.56
                ],
                [
                    "2025-03-04",
                    1.9
                ],
                [
                    "2025-03-05",
                    3.27
                ],
                [
                    "2025-03-06",
                    4.17
                ],
                [
                    "2025-03-07",
                    -0.69
                ],
                [
                    "2025-03-10",
                    -8.37
                ],
                [
                    "2025-03-27",
                    -2.74
                ],
                [
                    "2025-03-28",
                    1.54
                ],
                [
                    "2025-03-31",
                    1.02
                ],
                [
                    "2025-04-01",
                    4.0
                ],
                [
                    "2025-04-02",
                    0.83
                ],
                [
                    "2025-04-03",
                    14.19
                ],
                [
                    "2025-04-07",
                    10.52
                ],
                [
                    "2025-04-08",
                    5.85
                ],
                [
                    "2025-04-09",
                    -1.42
                ],
                [
                    "2025-04-10",
                    7.96
                ],
                [
                    "2025-04-11",
                    8.09
                ],
                [
                    "2025-04-14",
                    1.83
                ],
                [
                    "2025-04-15",
                    16.14
                ],
                [
                    "2025-04-16",
                    4.97
                ],
                [
                    "2025-04-17",
                    -0.84
                ],
                [
                    "2025-04-18",
                    -10.8
                ],
                [
                    "2025-04-22",
                    16.94
                ],
                [
                    "2025-04-23",
                    1.68
                ],
                [
                    "2025-04-24",
                    16.53
                ],
                [
                    "2025-04-25",
                    7.52
                ],
                [
                    "2025-04-28",
                    6.29
                ],
                [
                    "2025-04-29",
                    -3.02
                ],
                [
                    "2025-04-30",
                    -3.02
                ],
                [
                    "2025-05-06",
                    5.91
                ],
                [
                    "2025-05-07",
                    -3.67
                ],
                [
                    "2025-05-09",
                    9.94
                ],
                [
                    "2025-05-12",
                    0.99
                ],
                [
                    "2025-05-13",
                    12.73
                ],
                [
                    "2025-05-14",
                    5.15
                ],
                [
                    "2025-05-15",
                    11.09
                ],
                [
                    "2025-05-16",
                    -5.36
                ],
                [
                    "2025-05-19",
                    3.73
                ],
                [
                    "2025-05-20",
                    -7.8
                ],
                [
                    "2025-05-21",
                    8.87
                ],
                [
                    "2025-05-22",
                    15.21
                ],
                [
                    "2025-05-23",
                    2.97
                ],
                [
                    "2025-05-26",
                    -3.34
                ],
                [
                    "2025-05-27",
                    13.82
                ],
                [
                    "2025-05-28",
                    -12.51
                ],
                [
                    "2025-05-29",
                    4.49
                ],
                [
                    "2025-05-30",
                    -0.7
                ],
                [
                    "2025-06-05",
                    8.04
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-02",
                "2024-12-03",
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "601890 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_850b50b22e0442f8a0a6a0376acbfc59.setOption(option_850b50b22e0442f8a0a6a0376acbfc59);
            window.addEventListener('resize', function(){
                chart_850b50b22e0442f8a0a6a0376acbfc59.resize();
            })
    </script>

<script>
// 等待页面完全加载
window.addEventListener('load', function() {
    setTimeout(function() {
        // 获取所有图表实例
        var charts = [];
        var chartDoms = document.querySelectorAll('[_echarts_instance_]');
        chartDoms.forEach(function(dom) {
            var chart = echarts.getInstanceByDom(dom);
            if (chart) {
                charts.push(chart);
            }
        });

        console.log('找到图表数量:', charts.length);

        if (charts.length >= 2) {
            // 确保缩放联动
            echarts.connect(charts);
            console.log('缩放联动已设置');

            var chart1 = charts[0];
            var chart2 = charts[1];

            // 简单的鼠标悬停联动
            var isUpdating = false;

            chart1.on('mousemove', function(params) {
                if (!isUpdating && params.dataIndex !== undefined) {
                    isUpdating = true;
                    chart2.dispatchAction({
                        type: 'showTip',
                        seriesIndex: 0,
                        dataIndex: params.dataIndex
                    });
                    setTimeout(function() { isUpdating = false; }, 10);
                }
            });

            chart2.on('mousemove', function(params) {
                if (!isUpdating && params.dataIndex !== undefined) {
                    isUpdating = true;
                    chart1.dispatchAction({
                        type: 'showTip',
                        seriesIndex: 0,
                        dataIndex: params.dataIndex
                    });
                    setTimeout(function() { isUpdating = false; }, 10);
                }
            });

            console.log('鼠标悬停联动已设置');
        }
    }, 2000);
});
</script>

</body>
</html>
