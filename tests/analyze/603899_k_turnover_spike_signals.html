<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="b6c3e8afd4714ad3a92f5dca405d530b" class="chart-container" style="width:100%; height:450px; "></div>
    <script>
        var chart_b6c3e8afd4714ad3a92f5dca405d530b = echarts.init(
            document.getElementById('b6c3e8afd4714ad3a92f5dca405d530b'), 'white', {renderer: 'canvas'});
        var option_b6c3e8afd4714ad3a92f5dca405d530b = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    33.2,
                    32.83,
                    32.2,
                    33.27
                ],
                [
                    32.96,
                    32.15,
                    31.84,
                    32.96
                ],
                [
                    31.78,
                    31.32,
                    30.93,
                    32.08
                ],
                [
                    31.01,
                    31.3,
                    30.78,
                    31.32
                ],
                [
                    31.35,
                    31.5,
                    30.93,
                    31.59
                ],
                [
                    31.31,
                    30.9,
                    30.84,
                    31.59
                ],
                [
                    32.0,
                    31.06,
                    30.98,
                    32.6
                ],
                [
                    30.92,
                    31.74,
                    30.89,
                    31.76
                ],
                [
                    31.75,
                    33.28,
                    31.55,
                    33.77
                ],
                [
                    33.07,
                    32.65,
                    32.53,
                    33.68
                ],
                [
                    32.32,
                    31.88,
                    31.52,
                    32.95
                ],
                [
                    31.55,
                    31.87,
                    31.26,
                    32.11
                ],
                [
                    31.87,
                    32.38,
                    31.19,
                    32.68
                ],
                [
                    31.9,
                    31.71,
                    31.65,
                    32.44
                ],
                [
                    31.6,
                    32.04,
                    31.54,
                    32.76
                ],
                [
                    31.82,
                    30.96,
                    30.85,
                    32.09
                ],
                [
                    30.72,
                    31.08,
                    30.63,
                    31.15
                ],
                [
                    31.0,
                    30.55,
                    30.3,
                    31.07
                ],
                [
                    30.45,
                    30.36,
                    30.24,
                    30.59
                ],
                [
                    30.4,
                    30.6,
                    30.12,
                    30.85
                ],
                [
                    30.55,
                    30.28,
                    30.15,
                    30.86
                ],
                [
                    30.2,
                    30.25,
                    29.92,
                    30.65
                ],
                [
                    30.19,
                    29.49,
                    29.2,
                    30.54
                ],
                [
                    29.5,
                    28.87,
                    28.78,
                    29.84
                ],
                [
                    28.91,
                    28.76,
                    28.47,
                    29.14
                ],
                [
                    28.76,
                    28.72,
                    28.28,
                    28.88
                ],
                [
                    28.64,
                    28.43,
                    27.94,
                    28.65
                ],
                [
                    28.23,
                    28.12,
                    28.01,
                    28.48
                ],
                [
                    28.25,
                    27.85,
                    27.85,
                    28.31
                ],
                [
                    27.51,
                    28.19,
                    27.44,
                    28.3
                ],
                [
                    28.19,
                    28.73,
                    28.1,
                    28.83
                ],
                [
                    28.74,
                    28.35,
                    28.29,
                    28.79
                ],
                [
                    28.5,
                    28.47,
                    28.25,
                    28.84
                ],
                [
                    28.32,
                    28.39,
                    28.25,
                    28.62
                ],
                [
                    28.55,
                    28.47,
                    28.37,
                    28.84
                ],
                [
                    28.6,
                    28.58,
                    28.17,
                    28.68
                ],
                [
                    28.36,
                    28.29,
                    28.06,
                    28.41
                ],
                [
                    28.3,
                    28.15,
                    28.15,
                    28.71
                ],
                [
                    28.11,
                    28.39,
                    28.08,
                    28.4
                ],
                [
                    28.4,
                    28.4,
                    28.32,
                    28.7
                ],
                [
                    28.58,
                    27.81,
                    27.74,
                    28.62
                ],
                [
                    27.82,
                    28.18,
                    27.68,
                    28.25
                ],
                [
                    28.18,
                    28.49,
                    27.95,
                    28.6
                ],
                [
                    28.49,
                    28.72,
                    28.25,
                    28.78
                ],
                [
                    28.71,
                    28.34,
                    28.22,
                    28.73
                ],
                [
                    28.2,
                    28.46,
                    28.1,
                    28.46
                ],
                [
                    28.47,
                    28.77,
                    28.34,
                    29.05
                ],
                [
                    28.75,
                    29.08,
                    28.7,
                    29.08
                ],
                [
                    29.32,
                    28.76,
                    28.56,
                    29.44
                ],
                [
                    28.7,
                    27.99,
                    27.9,
                    28.72
                ],
                [
                    27.96,
                    27.88,
                    27.61,
                    28.09
                ],
                [
                    27.8,
                    28.09,
                    27.7,
                    28.28
                ],
                [
                    28.15,
                    27.8,
                    27.52,
                    28.27
                ],
                [
                    27.7,
                    27.7,
                    27.52,
                    27.83
                ],
                [
                    27.53,
                    27.08,
                    27.04,
                    27.56
                ],
                [
                    27.02,
                    27.34,
                    27.02,
                    27.39
                ],
                [
                    27.39,
                    27.97,
                    27.21,
                    28.05
                ],
                [
                    28.01,
                    27.61,
                    27.57,
                    28.63
                ],
                [
                    27.62,
                    27.53,
                    27.42,
                    28.05
                ],
                [
                    27.47,
                    27.4,
                    27.17,
                    27.52
                ],
                [
                    27.44,
                    27.29,
                    27.05,
                    27.49
                ],
                [
                    27.37,
                    27.78,
                    27.3,
                    28.01
                ],
                [
                    27.7,
                    27.85,
                    27.52,
                    27.94
                ],
                [
                    27.85,
                    27.81,
                    27.64,
                    27.94
                ],
                [
                    27.58,
                    28.6,
                    27.46,
                    28.6
                ],
                [
                    28.75,
                    29.77,
                    28.46,
                    30.1
                ],
                [
                    29.76,
                    29.71,
                    29.62,
                    30.7
                ],
                [
                    29.72,
                    30.63,
                    29.72,
                    30.76
                ],
                [
                    30.7,
                    30.15,
                    30.02,
                    30.88
                ],
                [
                    30.29,
                    30.12,
                    29.68,
                    30.35
                ],
                [
                    30.0,
                    29.63,
                    29.44,
                    30.29
                ],
                [
                    29.63,
                    29.21,
                    29.17,
                    29.74
                ],
                [
                    29.26,
                    29.33,
                    29.2,
                    30.26
                ],
                [
                    29.26,
                    29.04,
                    28.8,
                    29.59
                ],
                [
                    29.14,
                    29.21,
                    28.85,
                    29.25
                ],
                [
                    29.3,
                    30.13,
                    28.92,
                    30.3
                ],
                [
                    30.09,
                    31.04,
                    29.82,
                    31.2
                ],
                [
                    30.83,
                    30.68,
                    30.32,
                    31.08
                ],
                [
                    30.61,
                    30.59,
                    30.26,
                    31.34
                ],
                [
                    30.59,
                    31.81,
                    30.58,
                    32.41
                ],
                [
                    32.5,
                    31.64,
                    31.49,
                    33.08
                ],
                [
                    31.5,
                    31.6,
                    31.22,
                    32.2
                ],
                [
                    29.5,
                    28.44,
                    28.44,
                    30.45
                ],
                [
                    28.52,
                    29.56,
                    28.52,
                    29.8
                ],
                [
                    29.31,
                    29.93,
                    28.72,
                    30.37
                ],
                [
                    30.41,
                    30.85,
                    30.0,
                    31.24
                ],
                [
                    30.58,
                    30.35,
                    30.08,
                    31.03
                ],
                [
                    30.39,
                    30.55,
                    30.39,
                    31.15
                ],
                [
                    30.55,
                    30.59,
                    30.25,
                    31.18
                ],
                [
                    30.59,
                    29.87,
                    29.29,
                    30.7
                ],
                [
                    29.58,
                    29.65,
                    29.32,
                    29.98
                ],
                [
                    29.58,
                    30.01,
                    29.58,
                    30.16
                ],
                [
                    29.98,
                    31.6,
                    29.51,
                    31.84
                ],
                [
                    31.59,
                    31.41,
                    31.03,
                    31.84
                ],
                [
                    31.69,
                    31.17,
                    30.9,
                    31.69
                ],
                [
                    31.17,
                    31.55,
                    31.06,
                    32.05
                ],
                [
                    32.0,
                    31.22,
                    31.22,
                    32.18
                ],
                [
                    31.22,
                    31.32,
                    31.13,
                    31.91
                ],
                [
                    30.58,
                    29.47,
                    28.95,
                    30.58
                ],
                [
                    29.47,
                    29.42,
                    29.39,
                    30.05
                ],
                [
                    29.6,
                    30.82,
                    29.29,
                    30.96
                ],
                [
                    31.0,
                    30.25,
                    30.0,
                    31.14
                ],
                [
                    30.11,
                    30.12,
                    30.01,
                    30.33
                ],
                [
                    30.13,
                    30.05,
                    29.81,
                    30.28
                ],
                [
                    30.06,
                    29.71,
                    29.58,
                    30.21
                ],
                [
                    30.01,
                    29.83,
                    29.71,
                    30.15
                ],
                [
                    29.73,
                    29.91,
                    29.53,
                    30.05
                ],
                [
                    29.9,
                    29.39,
                    29.37,
                    30.25
                ],
                [
                    29.5,
                    29.16,
                    29.05,
                    29.5
                ],
                [
                    28.35,
                    27.83,
                    27.5,
                    28.38
                ],
                [
                    27.82,
                    28.74,
                    27.81,
                    29.06
                ],
                [
                    28.7,
                    28.63,
                    28.33,
                    28.94
                ],
                [
                    28.52,
                    28.23,
                    28.12,
                    28.58
                ],
                [
                    28.2,
                    27.7,
                    27.69,
                    28.33
                ],
                [
                    27.83,
                    28.21,
                    27.6,
                    28.37
                ],
                [
                    28.21,
                    28.7,
                    28.03,
                    28.96
                ],
                [
                    28.71,
                    29.57,
                    28.6,
                    29.66
                ],
                [
                    29.41,
                    29.48,
                    29.0,
                    29.78
                ],
                [
                    29.49,
                    29.51,
                    29.28,
                    29.88
                ],
                [
                    29.36,
                    30.71,
                    29.28,
                    31.09
                ],
                [
                    30.7,
                    30.64,
                    30.5,
                    30.95
                ],
                [
                    30.66,
                    31.76,
                    30.63,
                    32.4
                ],
                [
                    31.75,
                    31.53,
                    31.08,
                    31.85
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "\u5806\u91cf\u6d3e\u53d1",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbol": "diamond",
            "symbolSize": 12,
            "data": [
                [
                    "2025-04-29",
                    30.58
                ]
            ],
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#d62728"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "\u5806\u91cf\u6d3e\u53d1"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-02",
                "2024-12-03",
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u91cf\u80fd-\u6362\u624b\u5806\u79ef\u4e09\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 20,
                "end": 80,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "8%",
            "right": "2%",
            "height": "85%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_b6c3e8afd4714ad3a92f5dca405d530b.setOption(option_b6c3e8afd4714ad3a92f5dca405d530b);
            window.addEventListener('resize', function(){
                chart_b6c3e8afd4714ad3a92f5dca405d530b.resize();
            })
    </script>
</body>
</html>
