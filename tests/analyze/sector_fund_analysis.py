# -*- coding: utf-8 -*-
"""
行业主力资金流入统计图表生成器
生成4个统计图：
1. 近25日行业主力资金流入 (亿元) - 流入红色，流出绿色
2. 近5日、10日、15日主力资金流入统计图 - 前15名
3. 近25日所有行业的资金流入趋势图
4. 近25日主力资金流入前15的趋势图
"""

import pandas as pd
import numpy as np
from pyecharts.charts import Bar, Line, Grid
from pyecharts import options as opts
from datetime import datetime, timedelta
import re

def load_and_process_data(csv_path):
    """加载并预处理数据"""
    df = pd.read_csv(csv_path)
    df["date"] = pd.to_datetime(df["date"])
    
    # 计算主力资金净流入 (主力 = major + super)
    df["main_net_inflow"] = df["major_net_inflow"] + df["super_net_inflow"]
    
    return df

def get_time_window_data(df, days):
    """获取指定天数的数据窗口"""
    latest = df["date"].max()
    start_date = latest - pd.Timedelta(days=days-1)
    return df[df["date"] >= start_date]

def create_chart1_main_inflow_bar(df):
    """图表1: 近25日行业主力资金流入柱状图"""
    # 获取近25日数据
    win25 = get_time_window_data(df, 25)
    
    # 按行业汇总主力资金流入 (转换为亿元)
    sector_sum = win25.groupby("name")["main_net_inflow"].sum() / 1e8
    sector_sum = sector_sum.sort_values(ascending=True)  # 升序排列便于水平柱状图显示
    
    # 分离流入和流出
    inflow_data = []
    outflow_data = []
    sectors = []
    
    for sector, value in sector_sum.items():
        sectors.append(sector)
        if value >= 0:
            inflow_data.append(round(value, 2))
            outflow_data.append(0)
        else:
            inflow_data.append(0)
            outflow_data.append(round(value, 2))
    
    bar = (
        Bar(init_opts=opts.InitOpts(width="1200px", height="800px"))
        .add_xaxis(sectors)
        .add_yaxis(
            "资金流入",
            inflow_data,
            itemstyle_opts=opts.ItemStyleOpts(color="#ff4444"),  # 红色表示流入
            label_opts=opts.LabelOpts(is_show=True, position="right")
        )
        .add_yaxis(
            "资金流出", 
            outflow_data,
            itemstyle_opts=opts.ItemStyleOpts(color="#00aa44"),  # 绿色表示流出
            label_opts=opts.LabelOpts(is_show=True, position="right")
        )
        .reversal_axis()
        .set_global_opts(
            title_opts=opts.TitleOpts(title="近25日行业主力资金流入 (亿元)", subtitle="红色=流入，绿色=流出"),
            xaxis_opts=opts.AxisOpts(name="资金流入 (亿元)"),
            yaxis_opts=opts.AxisOpts(name="行业"),
            legend_opts=opts.LegendOpts(pos_top="5%"),
            tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="shadow")
        )
    )
    
    return bar

def create_chart2_multi_period_comparison(df):
    """图表2: 近5日、10日、15日主力资金流入统计图 - 前15名"""
    periods = [5, 10, 15]
    period_data = {}
    
    # 计算各时间段的数据
    for period in periods:
        win = get_time_window_data(df, period)
        sector_sum = win.groupby("name")["main_net_inflow"].sum() / 1e8
        # 取前15名（按流入金额）
        top15 = sector_sum.nlargest(15)
        period_data[f"{period}日"] = top15
    
    # 获取所有出现在前15的行业
    all_sectors = set()
    for data in period_data.values():
        all_sectors.update(data.index)
    all_sectors = sorted(list(all_sectors))
    
    bar = (
        Bar(init_opts=opts.InitOpts(width="1400px", height="700px"))
        .add_xaxis(all_sectors)
    )
    
    colors = ["#ff6b6b", "#4ecdc4", "#45b7d1"]
    for i, (period_name, data) in enumerate(period_data.items()):
        values = [round(data.get(sector, 0), 2) for sector in all_sectors]
        bar.add_yaxis(
            period_name,
            values,
            itemstyle_opts=opts.ItemStyleOpts(color=colors[i]),
            label_opts=opts.LabelOpts(is_show=False)
        )
    
    bar.set_global_opts(
        title_opts=opts.TitleOpts(title="近5日、10日、15日主力资金流入对比 (前15行业)", subtitle="单位：亿元"),
        xaxis_opts=opts.AxisOpts(
            name="行业",
            axislabel_opts=opts.LabelOpts(rotate=-45)
        ),
        yaxis_opts=opts.AxisOpts(name="主力资金流入 (亿元)"),
        legend_opts=opts.LegendOpts(
            pos_top="5%",
            orient="horizontal",
            item_width=25,
            item_height=14
        ),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="shadow"),
        datazoom_opts=[opts.DataZoomOpts(type_="slider", range_start=0, range_end=100)]
    )
    
    return bar

def create_chart3_15day_top15_trend(df):
    """图表3: 近15日主力资金流入前15行业趋势"""
    win15 = get_time_window_data(df, 15)

    # 找出15日累计流入前15的行业
    sector_total = win15.groupby("name")["main_net_inflow"].sum()
    top15_sectors = sector_total.nlargest(15).index.tolist()

    # 筛选前15行业的数据
    top15_data = win15[win15["name"].isin(top15_sectors)]

    # 按日期和行业透视
    pivot = top15_data.pivot_table(
        index="date",
        columns="name",
        values="main_net_inflow",
        aggfunc="sum"
    ) / 1e8  # 转换为亿元

    dates = pivot.index.strftime("%m-%d").tolist()
    sectors = top15_sectors  # 使用排序后的前15行业列表
    
    line = (
        Line(init_opts=opts.InitOpts(width="1400px", height="1000px"))
        .add_xaxis(dates)
    )
    
    # 为前30行业添加一条线，使用不同颜色
    colors = ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57", "#ff9ff3",
              "#54a0ff", "#5f27cd", "#00d2d3", "#ff9f43", "#10ac84", "#ee5a24",
              "#2d3436", "#636e72", "#a29bfe", "#fd79a8", "#fdcb6e", "#6c5ce7",
              "#74b9ff", "#00b894", "#e17055", "#81ecec", "#fab1a0", "#fd79a8",
              "#e84393", "#00cec9", "#55a3ff", "#ff7675", "#74b9ff", "#00b894"]

    for i, sector in enumerate(sectors):
        if sector in pivot.columns:
            values = [round(v, 2) if not pd.isna(v) else 0 for v in pivot[sector]]
            color = colors[i % len(colors)]

            line.add_yaxis(
                sector,
                values,
                is_smooth=True,
                is_symbol_show=False,
                linestyle_opts=opts.LineStyleOpts(width=1.5, color=color),
                label_opts=opts.LabelOpts(is_show=False)
            )
    
    line.set_global_opts(
        title_opts=opts.TitleOpts(title="近25日前30行业资金流入趋势", subtitle="单位：亿元", pos_top="300px"),
        xaxis_opts=opts.AxisOpts(
            type_="category",
            boundary_gap=False,
            axislabel_opts=opts.LabelOpts(rotate=-45)
        ),
        yaxis_opts=opts.AxisOpts(name="主力资金流入 (亿元)"),

        legend_opts=opts.LegendOpts(
            pos_right="2%",
            pos_top="10%",
            type_="scroll",
            orient="vertical",
            item_width=15,
            item_height=12,
            item_gap=3
        ),
        tooltip_opts=opts.TooltipOpts(
            trigger="axis",
            axis_pointer_type="cross",
            position="top",
            background_color="rgba(255,255,255,0.9)",
            border_color="#ccc",
            border_width=1
        ),
        datazoom_opts=[
            opts.DataZoomOpts(type_="inside"),
            opts.DataZoomOpts(type_="slider", range_start=0, range_end=100)
        ]
    )
    
    # 使用Grid来调整布局
    grid = (
        Grid(init_opts=opts.InitOpts(width="1400px", height="1000px"))
        .add(line, grid_opts=opts.GridOpts(pos_top="350px"))
    )

    return grid

def create_chart4_top15_trend(df):
    """图表4: 近25日主力资金流入前15的趋势图"""
    win25 = get_time_window_data(df, 25)
    
    # 找出25日累计流入前15的行业
    sector_total = win25.groupby("name")["main_net_inflow"].sum()
    top15_sectors = sector_total.nlargest(15).index.tolist()
    
    # 筛选前15行业的数据
    top15_data = win25[win25["name"].isin(top15_sectors)]
    
    # 按日期和行业透视
    pivot = top15_data.pivot_table(
        index="date", 
        columns="name", 
        values="main_net_inflow", 
        aggfunc="sum"
    ) / 1e8  # 转换为亿元
    
    dates = pivot.index.strftime("%m-%d").tolist()
    
    line = (
        Line(init_opts=opts.InitOpts(width="1400px", height="1000px"))
        .add_xaxis(dates)
    )
    
    # 为前15行业添加趋势线
    colors = ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57", "#ff9ff3", 
              "#54a0ff", "#5f27cd", "#00d2d3", "#ff9f43", "#10ac84", "#ee5a24",
              "#2d3436", "#636e72", "#a29bfe"]
    
    for i, sector in enumerate(top15_sectors):
        if sector in pivot.columns:
            values = [round(v, 2) if not pd.isna(v) else 0 for v in pivot[sector]]
            
            line.add_yaxis(
                sector,
                values,
                is_smooth=True,
                is_symbol_show=True,
                symbol_size=4,
                linestyle_opts=opts.LineStyleOpts(width=2, color=colors[i]),
                label_opts=opts.LabelOpts(is_show=False)
            )
    
    line.set_global_opts(
        title_opts=opts.TitleOpts(title="近25日主力资金流入前15行业趋势", subtitle="单位：亿元", pos_top="300px"),
        xaxis_opts=opts.AxisOpts(
            type_="category",
            boundary_gap=False,
            axislabel_opts=opts.LabelOpts(rotate=-45)
        ),
        yaxis_opts=opts.AxisOpts(name="主力资金流入 (亿元)"),

        legend_opts=opts.LegendOpts(
            pos_right="2%",
            pos_top="10%",
            type_="scroll",
            orient="vertical",
            item_width=15,
            item_height=12,
            item_gap=3
        ),
        tooltip_opts=opts.TooltipOpts(
            trigger="axis",
            axis_pointer_type="cross",
            position="top",
            background_color="rgba(255,255,255,0.9)",
            border_color="#ccc",
            border_width=1
        ),
        datazoom_opts=[
            opts.DataZoomOpts(type_="inside"),
            opts.DataZoomOpts(type_="slider", range_start=0, range_end=100)
        ]
    )
    
    # 使用Grid来调整布局
    grid = (
        Grid(init_opts=opts.InitOpts(width="1400px", height="1000px"))
        .add(line, grid_opts=opts.GridOpts(pos_top="350px"))
    )

    return grid

def main():
    """主函数：生成所有图表"""
    csv_path = "/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/blank_fund.csv"
    
    # 加载数据
    print("正在加载数据...")
    df = load_and_process_data(csv_path)
    print(f"数据加载完成，共 {len(df)} 条记录")
    
    # 生成图表1
    print("正在生成图表1: 近25日行业主力资金流入...")
    chart1 = create_chart1_main_inflow_bar(df)
    chart1.render("/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/chart1_25day_inflow.html")
    
    # 生成图表2
    print("正在生成图表2: 多时间段对比...")
    chart2 = create_chart2_multi_period_comparison(df)
    chart2.render("/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/chart2_multi_period.html")
    
    # 生成图表3
    print("正在生成图表3: 前30行业趋势...")
    chart3 = create_chart3_all_sectors_trend(df)
    chart3.render("/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/chart3_top30_trends.html")
    
    # 生成图表4
    print("正在生成图表4: 前15行业趋势...")
    chart4 = create_chart4_top15_trend(df)
    chart4.render("/Users/<USER>/Desktop/TT/code/trading-system/tests/analyze/chart4_top15_trends.html")
    
    print("所有图表生成完成！")
    print("文件位置：")
    print("- chart1_25day_inflow.html: 近25日行业主力资金流入")
    print("- chart2_multi_period.html: 5日、10日、15日对比")
    print("- chart3_top30_trends.html: 前30行业趋势")
    print("- chart4_top15_trends.html: 前15行业趋势")

if __name__ == "__main__":
    main()
