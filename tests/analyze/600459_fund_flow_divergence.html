<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="84dab334bdee466590b70bbbde9d42dd" class="chart-container" style="width:100%; height:750px; "></div>
    <script>
        var chart_84dab334bdee466590b70bbbde9d42dd = echarts.init(
            document.getElementById('84dab334bdee466590b70bbbde9d42dd'), 'white', {renderer: 'canvas'});
            
    // 等待页面加载完成
    setTimeout(function() {
        // 获取所有图表实例
        var charts = [];
        var chartDoms = document.querySelectorAll('[_echarts_instance_]');
        chartDoms.forEach(function(dom) {
            var chart = echarts.getInstanceByDom(dom);
            if (chart) {
                charts.push(chart);
            }
        });

        console.log('找到图表数量:', charts.length);

        // 设置缩放联动
        if (charts.length >= 2) {
            echarts.connect(charts);
            console.log('图表缩放联动已设置');
        }
    }, 100);

        var option_84dab334bdee466590b70bbbde9d42dd = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    14.05,
                    14.19,
                    13.98,
                    14.2
                ],
                [
                    14.16,
                    14.2,
                    14.06,
                    14.25
                ],
                [
                    14.25,
                    14.03,
                    14.0,
                    14.29
                ],
                [
                    14.0,
                    14.03,
                    13.84,
                    14.06
                ],
                [
                    14.03,
                    14.16,
                    13.9,
                    14.25
                ],
                [
                    14.18,
                    14.15,
                    14.06,
                    14.34
                ],
                [
                    14.4,
                    14.19,
                    14.17,
                    14.49
                ],
                [
                    14.18,
                    14.36,
                    14.12,
                    14.4
                ],
                [
                    14.32,
                    14.47,
                    14.27,
                    14.5
                ],
                [
                    14.41,
                    14.16,
                    14.15,
                    14.43
                ],
                [
                    14.2,
                    14.17,
                    14.12,
                    14.23
                ],
                [
                    14.16,
                    14.1,
                    14.02,
                    14.24
                ],
                [
                    14.12,
                    14.01,
                    13.98,
                    14.18
                ],
                [
                    13.95,
                    14.03,
                    13.79,
                    14.07
                ],
                [
                    14.02,
                    14.0,
                    13.95,
                    14.07
                ],
                [
                    14.0,
                    13.84,
                    13.82,
                    14.05
                ],
                [
                    13.84,
                    14.04,
                    13.84,
                    14.05
                ],
                [
                    14.08,
                    14.04,
                    13.93,
                    14.1
                ],
                [
                    14.04,
                    13.95,
                    13.93,
                    14.1
                ],
                [
                    13.94,
                    14.11,
                    13.92,
                    14.18
                ],
                [
                    14.06,
                    14.07,
                    14.01,
                    14.11
                ],
                [
                    14.05,
                    13.73,
                    13.71,
                    14.1
                ],
                [
                    13.78,
                    13.45,
                    13.38,
                    13.89
                ],
                [
                    13.5,
                    13.42,
                    13.37,
                    13.85
                ],
                [
                    13.45,
                    13.56,
                    13.31,
                    13.82
                ],
                [
                    13.56,
                    13.61,
                    13.44,
                    13.75
                ],
                [
                    13.56,
                    13.42,
                    13.18,
                    13.58
                ],
                [
                    13.31,
                    13.59,
                    13.31,
                    13.74
                ],
                [
                    13.69,
                    13.46,
                    13.46,
                    13.76
                ],
                [
                    13.52,
                    13.7,
                    13.42,
                    13.82
                ],
                [
                    13.64,
                    13.95,
                    13.59,
                    13.96
                ],
                [
                    13.96,
                    13.8,
                    13.75,
                    13.99
                ],
                [
                    13.85,
                    14.01,
                    13.85,
                    14.06
                ],
                [
                    14.19,
                    13.76,
                    13.74,
                    14.3
                ],
                [
                    13.78,
                    13.63,
                    13.61,
                    13.81
                ],
                [
                    13.66,
                    13.64,
                    13.5,
                    13.7
                ],
                [
                    13.6,
                    13.49,
                    13.45,
                    13.67
                ],
                [
                    13.55,
                    13.42,
                    13.42,
                    13.71
                ],
                [
                    13.42,
                    13.63,
                    13.38,
                    13.63
                ],
                [
                    13.68,
                    13.72,
                    13.57,
                    13.83
                ],
                [
                    13.86,
                    13.85,
                    13.73,
                    13.9
                ],
                [
                    13.82,
                    14.08,
                    13.81,
                    14.1
                ],
                [
                    14.02,
                    14.04,
                    13.95,
                    14.11
                ],
                [
                    13.99,
                    13.96,
                    13.93,
                    14.08
                ],
                [
                    14.01,
                    14.03,
                    13.98,
                    14.12
                ],
                [
                    13.99,
                    13.94,
                    13.83,
                    14.03
                ],
                [
                    13.92,
                    13.84,
                    13.84,
                    13.98
                ],
                [
                    13.84,
                    13.87,
                    13.75,
                    13.92
                ],
                [
                    13.87,
                    13.66,
                    13.62,
                    13.87
                ],
                [
                    13.66,
                    13.56,
                    13.51,
                    13.73
                ],
                [
                    13.57,
                    13.61,
                    13.56,
                    13.66
                ],
                [
                    13.62,
                    13.69,
                    13.59,
                    13.73
                ],
                [
                    13.7,
                    13.73,
                    13.66,
                    13.77
                ],
                [
                    13.72,
                    13.73,
                    13.56,
                    13.8
                ],
                [
                    13.69,
                    13.55,
                    13.52,
                    13.71
                ],
                [
                    13.55,
                    13.63,
                    13.53,
                    13.68
                ],
                [
                    13.64,
                    13.5,
                    13.39,
                    13.7
                ],
                [
                    13.47,
                    13.4,
                    13.36,
                    13.57
                ],
                [
                    13.45,
                    13.54,
                    13.41,
                    13.7
                ],
                [
                    13.51,
                    13.66,
                    13.45,
                    13.69
                ],
                [
                    13.6,
                    13.64,
                    13.47,
                    13.67
                ],
                [
                    13.67,
                    13.73,
                    13.63,
                    13.76
                ],
                [
                    13.66,
                    13.98,
                    13.66,
                    14.07
                ],
                [
                    14.06,
                    14.16,
                    14.05,
                    14.28
                ],
                [
                    14.16,
                    14.59,
                    14.07,
                    14.63
                ],
                [
                    14.53,
                    14.36,
                    14.33,
                    14.62
                ],
                [
                    14.39,
                    14.5,
                    14.26,
                    14.52
                ],
                [
                    14.75,
                    14.5,
                    14.44,
                    14.88
                ],
                [
                    14.5,
                    14.32,
                    14.28,
                    14.51
                ],
                [
                    14.32,
                    14.46,
                    14.24,
                    14.63
                ],
                [
                    14.48,
                    14.42,
                    14.35,
                    14.53
                ],
                [
                    14.42,
                    14.34,
                    14.29,
                    14.47
                ],
                [
                    14.3,
                    14.16,
                    14.09,
                    14.42
                ],
                [
                    14.17,
                    14.29,
                    14.06,
                    14.38
                ],
                [
                    14.23,
                    14.44,
                    14.2,
                    14.51
                ],
                [
                    14.48,
                    14.35,
                    14.29,
                    14.61
                ],
                [
                    14.28,
                    14.17,
                    14.05,
                    14.3
                ],
                [
                    14.22,
                    14.07,
                    13.95,
                    14.27
                ],
                [
                    14.02,
                    13.95,
                    13.79,
                    14.1
                ],
                [
                    13.95,
                    14.03,
                    13.92,
                    14.12
                ],
                [
                    14.04,
                    13.88,
                    13.84,
                    14.06
                ],
                [
                    13.85,
                    13.88,
                    13.73,
                    13.92
                ],
                [
                    13.14,
                    12.49,
                    12.49,
                    13.21
                ],
                [
                    12.54,
                    12.45,
                    12.27,
                    12.68
                ],
                [
                    12.31,
                    12.55,
                    11.91,
                    12.6
                ],
                [
                    12.72,
                    12.78,
                    12.69,
                    12.91
                ],
                [
                    12.78,
                    12.92,
                    12.71,
                    13.03
                ],
                [
                    12.98,
                    13.09,
                    12.98,
                    13.17
                ],
                [
                    13.07,
                    13.08,
                    12.91,
                    13.1
                ],
                [
                    13.08,
                    12.97,
                    12.8,
                    13.13
                ],
                [
                    12.97,
                    12.95,
                    12.92,
                    13.18
                ],
                [
                    12.9,
                    12.91,
                    12.79,
                    12.98
                ],
                [
                    12.92,
                    13.22,
                    12.87,
                    13.27
                ],
                [
                    13.25,
                    13.42,
                    13.14,
                    13.47
                ],
                [
                    13.28,
                    13.14,
                    13.14,
                    13.41
                ],
                [
                    13.15,
                    13.0,
                    12.97,
                    13.23
                ],
                [
                    13.28,
                    13.2,
                    13.18,
                    13.45
                ],
                [
                    13.25,
                    13.14,
                    13.08,
                    13.29
                ],
                [
                    13.17,
                    13.11,
                    13.06,
                    13.25
                ],
                [
                    13.17,
                    13.18,
                    13.16,
                    13.39
                ],
                [
                    13.22,
                    13.45,
                    13.22,
                    13.54
                ],
                [
                    13.49,
                    13.49,
                    13.38,
                    13.56
                ],
                [
                    13.44,
                    13.42,
                    13.38,
                    13.55
                ],
                [
                    13.42,
                    13.39,
                    13.27,
                    13.45
                ],
                [
                    13.4,
                    13.54,
                    13.37,
                    13.62
                ],
                [
                    13.62,
                    13.44,
                    13.4,
                    13.62
                ],
                [
                    13.45,
                    13.54,
                    13.36,
                    13.58
                ],
                [
                    13.54,
                    13.39,
                    13.38,
                    13.56
                ],
                [
                    13.4,
                    13.42,
                    13.39,
                    13.59
                ],
                [
                    13.42,
                    13.41,
                    13.29,
                    13.49
                ],
                [
                    13.4,
                    13.5,
                    13.38,
                    13.55
                ],
                [
                    13.73,
                    13.94,
                    13.73,
                    14.09
                ],
                [
                    13.97,
                    14.08,
                    13.88,
                    14.64
                ],
                [
                    14.29,
                    14.42,
                    14.25,
                    14.68
                ],
                [
                    14.6,
                    14.49,
                    14.35,
                    14.65
                ],
                [
                    14.44,
                    14.22,
                    14.13,
                    14.45
                ],
                [
                    14.17,
                    14.13,
                    14.04,
                    14.25
                ],
                [
                    14.09,
                    14.09,
                    14.02,
                    14.19
                ],
                [
                    14.08,
                    14.08,
                    14.0,
                    14.21
                ],
                [
                    14.11,
                    14.33,
                    14.08,
                    14.51
                ],
                [
                    14.35,
                    14.5,
                    14.33,
                    14.59
                ],
                [
                    14.6,
                    14.51,
                    14.48,
                    14.89
                ],
                [
                    14.98,
                    15.47,
                    14.91,
                    15.57
                ]
            ],
            "itemStyle": {
                "color": "#ec0000",
                "color0": "#00da3c"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    1.14
                ],
                [
                    "2024-12-06",
                    -8.11
                ],
                [
                    "2024-12-09",
                    6.39
                ],
                [
                    "2024-12-10",
                    -7.76
                ],
                [
                    "2024-12-11",
                    0.84
                ],
                [
                    "2024-12-12",
                    -0.94
                ],
                [
                    "2024-12-13",
                    3.98
                ],
                [
                    "2024-12-16",
                    10.37
                ],
                [
                    "2024-12-17",
                    4.41
                ],
                [
                    "2024-12-18",
                    -3.91
                ],
                [
                    "2024-12-19",
                    -4.21
                ],
                [
                    "2024-12-20",
                    4.1
                ],
                [
                    "2024-12-23",
                    7.32
                ],
                [
                    "2024-12-24",
                    12.31
                ],
                [
                    "2024-12-25",
                    -1.42
                ],
                [
                    "2024-12-26",
                    -19.92
                ],
                [
                    "2024-12-27",
                    -0.52
                ],
                [
                    "2024-12-30",
                    -7.57
                ],
                [
                    "2024-12-31",
                    -2.64
                ],
                [
                    "2025-01-02",
                    -6.05
                ],
                [
                    "2025-01-03",
                    -4.98
                ],
                [
                    "2025-01-06",
                    -2.94
                ],
                [
                    "2025-01-07",
                    5.71
                ],
                [
                    "2025-01-08",
                    0.18
                ],
                [
                    "2025-01-09",
                    11.48
                ],
                [
                    "2025-01-10",
                    4.48
                ],
                [
                    "2025-01-13",
                    8.78
                ],
                [
                    "2025-01-14",
                    4.74
                ],
                [
                    "2025-01-15",
                    -1.27
                ],
                [
                    "2025-01-16",
                    0.98
                ],
                [
                    "2025-01-17",
                    -9.4
                ],
                [
                    "2025-01-20",
                    -9.11
                ],
                [
                    "2025-01-21",
                    -10.93
                ],
                [
                    "2025-01-22",
                    -18.1
                ],
                [
                    "2025-01-23",
                    -5.89
                ],
                [
                    "2025-01-24",
                    -7.4
                ],
                [
                    "2025-01-27",
                    10.19
                ],
                [
                    "2025-02-05",
                    8.5
                ],
                [
                    "2025-02-06",
                    1.63
                ],
                [
                    "2025-02-07",
                    -2.72
                ],
                [
                    "2025-02-10",
                    -1.05
                ],
                [
                    "2025-02-11",
                    3.67
                ],
                [
                    "2025-02-12",
                    -16.19
                ],
                [
                    "2025-02-13",
                    -7.93
                ],
                [
                    "2025-02-14",
                    -6.99
                ],
                [
                    "2025-02-17",
                    -4.41
                ],
                [
                    "2025-02-18",
                    -3.01
                ],
                [
                    "2025-02-19",
                    -3.9
                ],
                [
                    "2025-02-20",
                    9.73
                ],
                [
                    "2025-02-21",
                    -0.51
                ],
                [
                    "2025-02-24",
                    -4.08
                ],
                [
                    "2025-02-25",
                    -15.68
                ],
                [
                    "2025-02-26",
                    -14.77
                ],
                [
                    "2025-02-27",
                    -7.11
                ],
                [
                    "2025-02-28",
                    -1.09
                ],
                [
                    "2025-03-03",
                    -4.52
                ],
                [
                    "2025-03-04",
                    7.24
                ],
                [
                    "2025-03-05",
                    -0.57
                ],
                [
                    "2025-03-06",
                    -0.79
                ],
                [
                    "2025-03-07",
                    -9.99
                ],
                [
                    "2025-03-10",
                    -1.84
                ],
                [
                    "2025-03-11",
                    2.64
                ],
                [
                    "2025-03-12",
                    -9.62
                ],
                [
                    "2025-03-13",
                    -1.15
                ],
                [
                    "2025-03-14",
                    -2.38
                ],
                [
                    "2025-03-17",
                    -7.37
                ],
                [
                    "2025-03-18",
                    7.31
                ],
                [
                    "2025-03-19",
                    -12.89
                ],
                [
                    "2025-03-20",
                    -10.59
                ],
                [
                    "2025-03-21",
                    -9.94
                ],
                [
                    "2025-03-24",
                    -1.78
                ],
                [
                    "2025-03-25",
                    2.31
                ],
                [
                    "2025-03-26",
                    1.6
                ],
                [
                    "2025-03-27",
                    -9.25
                ],
                [
                    "2025-03-28",
                    -11.83
                ],
                [
                    "2025-03-31",
                    -10.68
                ],
                [
                    "2025-04-01",
                    -2.28
                ],
                [
                    "2025-04-02",
                    -5.11
                ],
                [
                    "2025-04-03",
                    2.09
                ],
                [
                    "2025-04-07",
                    -14.12
                ],
                [
                    "2025-04-08",
                    -7.87
                ],
                [
                    "2025-04-09",
                    -2.08
                ],
                [
                    "2025-04-10",
                    4.91
                ],
                [
                    "2025-04-11",
                    -0.46
                ],
                [
                    "2025-04-14",
                    4.31
                ],
                [
                    "2025-04-15",
                    4.81
                ],
                [
                    "2025-04-16",
                    -10.03
                ],
                [
                    "2025-04-17",
                    1.75
                ],
                [
                    "2025-04-18",
                    -2.2
                ],
                [
                    "2025-04-21",
                    -1.89
                ],
                [
                    "2025-04-22",
                    4.21
                ],
                [
                    "2025-04-23",
                    -12.31
                ],
                [
                    "2025-04-24",
                    -12.56
                ],
                [
                    "2025-04-25",
                    -1.65
                ],
                [
                    "2025-04-28",
                    -11.81
                ],
                [
                    "2025-04-29",
                    7.1
                ],
                [
                    "2025-04-30",
                    -5.92
                ],
                [
                    "2025-05-06",
                    -17.85
                ],
                [
                    "2025-05-07",
                    6.99
                ],
                [
                    "2025-05-08",
                    -2.56
                ],
                [
                    "2025-05-09",
                    -0.56
                ],
                [
                    "2025-05-12",
                    -16.34
                ],
                [
                    "2025-05-13",
                    -10.2
                ],
                [
                    "2025-05-14",
                    -13.9
                ],
                [
                    "2025-05-15",
                    -3.23
                ],
                [
                    "2025-05-16",
                    -1.44
                ],
                [
                    "2025-05-19",
                    -4.96
                ],
                [
                    "2025-05-20",
                    -0.27
                ],
                [
                    "2025-05-21",
                    12.4
                ],
                [
                    "2025-05-22",
                    -1.22
                ],
                [
                    "2025-05-23",
                    -1.53
                ],
                [
                    "2025-05-26",
                    -6.95
                ],
                [
                    "2025-05-27",
                    -17.68
                ],
                [
                    "2025-05-28",
                    -18.6
                ],
                [
                    "2025-05-29",
                    10.85
                ],
                [
                    "2025-05-30",
                    12.61
                ],
                [
                    "2025-06-03",
                    0.72
                ],
                [
                    "2025-06-04",
                    -6.68
                ],
                [
                    "2025-06-05",
                    -3.46
                ],
                [
                    "2025-06-06",
                    3.26
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#1f77b4"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6e38\u8d44\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    -5.99
                ],
                [
                    "2024-12-06",
                    2.55
                ],
                [
                    "2024-12-09",
                    -1.84
                ],
                [
                    "2024-12-10",
                    1.01
                ],
                [
                    "2024-12-11",
                    -4.03
                ],
                [
                    "2024-12-12",
                    -2.98
                ],
                [
                    "2024-12-13",
                    5.83
                ],
                [
                    "2024-12-16",
                    -5.49
                ],
                [
                    "2024-12-17",
                    -6.32
                ],
                [
                    "2024-12-18",
                    2.55
                ],
                [
                    "2024-12-19",
                    -1.86
                ],
                [
                    "2024-12-20",
                    -0.77
                ],
                [
                    "2024-12-23",
                    -5.73
                ],
                [
                    "2024-12-24",
                    -14.02
                ],
                [
                    "2024-12-25",
                    -0.6
                ],
                [
                    "2024-12-26",
                    12.68
                ],
                [
                    "2024-12-27",
                    -2.37
                ],
                [
                    "2024-12-30",
                    1.42
                ],
                [
                    "2024-12-31",
                    1.98
                ],
                [
                    "2025-01-02",
                    -4.71
                ],
                [
                    "2025-01-03",
                    -4.17
                ],
                [
                    "2025-01-06",
                    -1.47
                ],
                [
                    "2025-01-07",
                    5.26
                ],
                [
                    "2025-01-08",
                    5.08
                ],
                [
                    "2025-01-09",
                    -9.22
                ],
                [
                    "2025-01-10",
                    -5.02
                ],
                [
                    "2025-01-13",
                    -8.54
                ],
                [
                    "2025-01-14",
                    -7.14
                ],
                [
                    "2025-01-15",
                    4.41
                ],
                [
                    "2025-01-16",
                    -6.26
                ],
                [
                    "2025-01-17",
                    -1.87
                ],
                [
                    "2025-01-20",
                    6.07
                ],
                [
                    "2025-01-21",
                    -0.77
                ],
                [
                    "2025-01-22",
                    -1.85
                ],
                [
                    "2025-01-23",
                    -2.49
                ],
                [
                    "2025-01-24",
                    -5.21
                ],
                [
                    "2025-01-27",
                    -3.14
                ],
                [
                    "2025-02-05",
                    -3.23
                ],
                [
                    "2025-02-06",
                    -9.94
                ],
                [
                    "2025-02-07",
                    2.36
                ],
                [
                    "2025-02-10",
                    4.41
                ],
                [
                    "2025-02-11",
                    -4.17
                ],
                [
                    "2025-02-12",
                    8.56
                ],
                [
                    "2025-02-13",
                    2.77
                ],
                [
                    "2025-02-14",
                    2.88
                ],
                [
                    "2025-02-17",
                    7.49
                ],
                [
                    "2025-02-18",
                    -0.24
                ],
                [
                    "2025-02-19",
                    4.97
                ],
                [
                    "2025-02-20",
                    1.67
                ],
                [
                    "2025-02-21",
                    0.32
                ],
                [
                    "2025-02-24",
                    -0.83
                ],
                [
                    "2025-02-25",
                    11.07
                ],
                [
                    "2025-02-26",
                    0.16
                ],
                [
                    "2025-02-27",
                    8.33
                ],
                [
                    "2025-02-28",
                    -4.85
                ],
                [
                    "2025-03-03",
                    2.56
                ],
                [
                    "2025-03-04",
                    -4.98
                ],
                [
                    "2025-03-05",
                    -0.65
                ],
                [
                    "2025-03-06",
                    -1.86
                ],
                [
                    "2025-03-07",
                    -0.02
                ],
                [
                    "2025-03-10",
                    -5.09
                ],
                [
                    "2025-03-11",
                    -6.03
                ],
                [
                    "2025-03-12",
                    6.73
                ],
                [
                    "2025-03-13",
                    -7.12
                ],
                [
                    "2025-03-14",
                    2.91
                ],
                [
                    "2025-03-17",
                    -1.63
                ],
                [
                    "2025-03-18",
                    -1.56
                ],
                [
                    "2025-03-19",
                    6.14
                ],
                [
                    "2025-03-20",
                    6.36
                ],
                [
                    "2025-03-21",
                    1.77
                ],
                [
                    "2025-03-24",
                    -0.12
                ],
                [
                    "2025-03-25",
                    1.78
                ],
                [
                    "2025-03-26",
                    0.89
                ],
                [
                    "2025-03-27",
                    4.4
                ],
                [
                    "2025-03-28",
                    9.59
                ],
                [
                    "2025-03-31",
                    1.67
                ],
                [
                    "2025-04-01",
                    3.65
                ],
                [
                    "2025-04-02",
                    7.24
                ],
                [
                    "2025-04-03",
                    2.65
                ],
                [
                    "2025-04-07",
                    8.8
                ],
                [
                    "2025-04-08",
                    3.41
                ],
                [
                    "2025-04-09",
                    2.17
                ],
                [
                    "2025-04-10",
                    3.67
                ],
                [
                    "2025-04-11",
                    -7.74
                ],
                [
                    "2025-04-14",
                    -1.39
                ],
                [
                    "2025-04-15",
                    -2.25
                ],
                [
                    "2025-04-16",
                    -1.48
                ],
                [
                    "2025-04-17",
                    -1.95
                ],
                [
                    "2025-04-18",
                    1.55
                ],
                [
                    "2025-04-21",
                    -1.5
                ],
                [
                    "2025-04-22",
                    -4.44
                ],
                [
                    "2025-04-23",
                    3.1
                ],
                [
                    "2025-04-24",
                    2.01
                ],
                [
                    "2025-04-25",
                    -1.25
                ],
                [
                    "2025-04-28",
                    7.99
                ],
                [
                    "2025-04-29",
                    -3.51
                ],
                [
                    "2025-04-30",
                    -3.34
                ],
                [
                    "2025-05-06",
                    8.43
                ],
                [
                    "2025-05-07",
                    -0.87
                ],
                [
                    "2025-05-08",
                    -3.73
                ],
                [
                    "2025-05-09",
                    -4.24
                ],
                [
                    "2025-05-12",
                    0.16
                ],
                [
                    "2025-05-13",
                    3.18
                ],
                [
                    "2025-05-14",
                    4.79
                ],
                [
                    "2025-05-15",
                    8.58
                ],
                [
                    "2025-05-16",
                    -1.22
                ],
                [
                    "2025-05-19",
                    7.94
                ],
                [
                    "2025-05-20",
                    -2.55
                ],
                [
                    "2025-05-21",
                    -6.14
                ],
                [
                    "2025-05-22",
                    2.31
                ],
                [
                    "2025-05-23",
                    -2.58
                ],
                [
                    "2025-05-26",
                    -5.56
                ],
                [
                    "2025-05-27",
                    -6.35
                ],
                [
                    "2025-05-28",
                    -1.25
                ],
                [
                    "2025-05-29",
                    -1.85
                ],
                [
                    "2025-05-30",
                    -4.39
                ],
                [
                    "2025-06-03",
                    4.35
                ],
                [
                    "2025-06-04",
                    -5.88
                ],
                [
                    "2025-06-05",
                    -3.07
                ],
                [
                    "2025-06-06",
                    -7.05
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#9467bd"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u6563\u6237\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    4.85
                ],
                [
                    "2024-12-06",
                    5.56
                ],
                [
                    "2024-12-09",
                    -4.55
                ],
                [
                    "2024-12-10",
                    6.75
                ],
                [
                    "2024-12-11",
                    3.19
                ],
                [
                    "2024-12-12",
                    3.93
                ],
                [
                    "2024-12-13",
                    -9.81
                ],
                [
                    "2024-12-16",
                    -4.89
                ],
                [
                    "2024-12-17",
                    1.92
                ],
                [
                    "2024-12-18",
                    1.36
                ],
                [
                    "2024-12-19",
                    6.08
                ],
                [
                    "2024-12-20",
                    -3.33
                ],
                [
                    "2024-12-23",
                    -1.6
                ],
                [
                    "2024-12-24",
                    1.71
                ],
                [
                    "2024-12-25",
                    2.02
                ],
                [
                    "2024-12-26",
                    7.24
                ],
                [
                    "2024-12-27",
                    2.89
                ],
                [
                    "2024-12-30",
                    6.15
                ],
                [
                    "2024-12-31",
                    0.65
                ],
                [
                    "2025-01-02",
                    10.76
                ],
                [
                    "2025-01-03",
                    9.16
                ],
                [
                    "2025-01-06",
                    4.4
                ],
                [
                    "2025-01-07",
                    -10.97
                ],
                [
                    "2025-01-08",
                    -5.26
                ],
                [
                    "2025-01-09",
                    -2.26
                ],
                [
                    "2025-01-10",
                    0.53
                ],
                [
                    "2025-01-13",
                    -0.24
                ],
                [
                    "2025-01-14",
                    2.4
                ],
                [
                    "2025-01-15",
                    -3.14
                ],
                [
                    "2025-01-16",
                    5.28
                ],
                [
                    "2025-01-17",
                    11.28
                ],
                [
                    "2025-01-20",
                    3.03
                ],
                [
                    "2025-01-21",
                    11.71
                ],
                [
                    "2025-01-22",
                    19.95
                ],
                [
                    "2025-01-23",
                    8.38
                ],
                [
                    "2025-01-24",
                    12.61
                ],
                [
                    "2025-01-27",
                    -7.05
                ],
                [
                    "2025-02-05",
                    -5.27
                ],
                [
                    "2025-02-06",
                    8.31
                ],
                [
                    "2025-02-07",
                    0.37
                ],
                [
                    "2025-02-10",
                    -3.35
                ],
                [
                    "2025-02-11",
                    0.5
                ],
                [
                    "2025-02-12",
                    7.62
                ],
                [
                    "2025-02-13",
                    5.16
                ],
                [
                    "2025-02-14",
                    4.11
                ],
                [
                    "2025-02-17",
                    -3.09
                ],
                [
                    "2025-02-18",
                    3.25
                ],
                [
                    "2025-02-19",
                    -1.07
                ],
                [
                    "2025-02-20",
                    -11.39
                ],
                [
                    "2025-02-21",
                    0.2
                ],
                [
                    "2025-02-24",
                    4.91
                ],
                [
                    "2025-02-25",
                    4.61
                ],
                [
                    "2025-02-26",
                    14.6
                ],
                [
                    "2025-02-27",
                    -1.22
                ],
                [
                    "2025-02-28",
                    5.94
                ],
                [
                    "2025-03-03",
                    1.96
                ],
                [
                    "2025-03-04",
                    -2.26
                ],
                [
                    "2025-03-05",
                    1.22
                ],
                [
                    "2025-03-06",
                    2.66
                ],
                [
                    "2025-03-07",
                    10.01
                ],
                [
                    "2025-03-10",
                    6.93
                ],
                [
                    "2025-03-11",
                    3.39
                ],
                [
                    "2025-03-12",
                    2.89
                ],
                [
                    "2025-03-13",
                    8.27
                ],
                [
                    "2025-03-14",
                    -0.53
                ],
                [
                    "2025-03-17",
                    9.01
                ],
                [
                    "2025-03-18",
                    -5.75
                ],
                [
                    "2025-03-19",
                    6.74
                ],
                [
                    "2025-03-20",
                    4.24
                ],
                [
                    "2025-03-21",
                    8.17
                ],
                [
                    "2025-03-24",
                    1.91
                ],
                [
                    "2025-03-25",
                    -4.09
                ],
                [
                    "2025-03-26",
                    -2.49
                ],
                [
                    "2025-03-27",
                    4.84
                ],
                [
                    "2025-03-28",
                    2.24
                ],
                [
                    "2025-03-31",
                    9.02
                ],
                [
                    "2025-04-01",
                    -1.37
                ],
                [
                    "2025-04-02",
                    -2.14
                ],
                [
                    "2025-04-03",
                    -4.74
                ],
                [
                    "2025-04-07",
                    5.32
                ],
                [
                    "2025-04-08",
                    4.46
                ],
                [
                    "2025-04-09",
                    -0.09
                ],
                [
                    "2025-04-10",
                    -8.58
                ],
                [
                    "2025-04-11",
                    8.2
                ],
                [
                    "2025-04-14",
                    -2.92
                ],
                [
                    "2025-04-15",
                    -2.56
                ],
                [
                    "2025-04-16",
                    11.51
                ],
                [
                    "2025-04-17",
                    0.19
                ],
                [
                    "2025-04-18",
                    0.65
                ],
                [
                    "2025-04-21",
                    3.39
                ],
                [
                    "2025-04-22",
                    0.23
                ],
                [
                    "2025-04-23",
                    9.21
                ],
                [
                    "2025-04-24",
                    10.55
                ],
                [
                    "2025-04-25",
                    2.91
                ],
                [
                    "2025-04-28",
                    3.82
                ],
                [
                    "2025-04-29",
                    -3.59
                ],
                [
                    "2025-04-30",
                    9.26
                ],
                [
                    "2025-05-06",
                    9.42
                ],
                [
                    "2025-05-07",
                    -6.12
                ],
                [
                    "2025-05-08",
                    6.28
                ],
                [
                    "2025-05-09",
                    4.8
                ],
                [
                    "2025-05-12",
                    16.18
                ],
                [
                    "2025-05-13",
                    7.02
                ],
                [
                    "2025-05-14",
                    9.1
                ],
                [
                    "2025-05-15",
                    -5.34
                ],
                [
                    "2025-05-16",
                    2.66
                ],
                [
                    "2025-05-19",
                    -2.99
                ],
                [
                    "2025-05-20",
                    2.83
                ],
                [
                    "2025-05-21",
                    -6.27
                ],
                [
                    "2025-05-22",
                    -1.09
                ],
                [
                    "2025-05-23",
                    4.11
                ],
                [
                    "2025-05-26",
                    12.51
                ],
                [
                    "2025-05-27",
                    24.03
                ],
                [
                    "2025-05-28",
                    19.85
                ],
                [
                    "2025-05-29",
                    -9.0
                ],
                [
                    "2025-05-30",
                    -8.22
                ],
                [
                    "2025-06-03",
                    -5.08
                ],
                [
                    "2025-06-04",
                    12.56
                ],
                [
                    "2025-06-05",
                    6.53
                ],
                [
                    "2025-06-06",
                    3.79
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#ff7f0e"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "triangle",
            "symbolSize": 15,
            "data": [
                [
                    "2024-12-13",
                    3.98
                ],
                [
                    "2024-12-16",
                    10.37
                ],
                [
                    "2024-12-17",
                    4.41
                ],
                [
                    "2024-12-18",
                    -3.91
                ],
                [
                    "2024-12-19",
                    -4.21
                ],
                [
                    "2025-01-09",
                    11.48
                ],
                [
                    "2025-01-10",
                    4.48
                ],
                [
                    "2025-01-13",
                    8.78
                ],
                [
                    "2025-01-14",
                    4.74
                ],
                [
                    "2025-01-15",
                    -1.27
                ],
                [
                    "2025-02-10",
                    -1.05
                ],
                [
                    "2025-04-15",
                    4.81
                ],
                [
                    "2025-05-21",
                    12.4
                ],
                [
                    "2025-05-22",
                    -1.22
                ],
                [
                    "2025-05-23",
                    -1.53
                ],
                [
                    "2025-06-05",
                    -3.46
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "red"
            }
        },
        {
            "type": "scatter",
            "name": "\u6e38\u8d44\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "diamond",
            "symbolSize": 15,
            "data": [
                [
                    "2025-02-20",
                    1.67
                ],
                [
                    "2025-02-21",
                    0.32
                ],
                [
                    "2025-02-24",
                    -0.83
                ],
                [
                    "2025-02-25",
                    11.07
                ],
                [
                    "2025-04-10",
                    3.67
                ],
                [
                    "2025-05-21",
                    -6.14
                ],
                [
                    "2025-05-22",
                    2.31
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "gold"
            }
        },
        {
            "type": "scatter",
            "name": "\u6563\u6237\u80cc\u79bb",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "symbol": "circle",
            "symbolSize": 15,
            "data": [
                [
                    "2024-12-11",
                    3.19
                ],
                [
                    "2024-12-12",
                    3.93
                ],
                [
                    "2024-12-27",
                    2.89
                ],
                [
                    "2024-12-30",
                    6.15
                ],
                [
                    "2024-12-31",
                    0.65
                ],
                [
                    "2025-01-02",
                    10.76
                ],
                [
                    "2025-01-03",
                    9.16
                ],
                [
                    "2025-01-06",
                    4.4
                ],
                [
                    "2025-01-07",
                    -10.97
                ],
                [
                    "2025-01-08",
                    -5.26
                ],
                [
                    "2025-01-20",
                    3.03
                ],
                [
                    "2025-01-21",
                    11.71
                ],
                [
                    "2025-01-22",
                    19.95
                ],
                [
                    "2025-01-23",
                    8.38
                ],
                [
                    "2025-01-24",
                    12.61
                ],
                [
                    "2025-01-27",
                    -7.05
                ],
                [
                    "2025-02-05",
                    -5.27
                ],
                [
                    "2025-02-12",
                    7.62
                ],
                [
                    "2025-02-13",
                    5.16
                ],
                [
                    "2025-02-14",
                    4.11
                ],
                [
                    "2025-02-17",
                    -3.09
                ],
                [
                    "2025-02-18",
                    3.25
                ],
                [
                    "2025-02-19",
                    -1.07
                ],
                [
                    "2025-02-26",
                    14.6
                ],
                [
                    "2025-02-27",
                    -1.22
                ],
                [
                    "2025-02-28",
                    5.94
                ],
                [
                    "2025-03-03",
                    1.96
                ],
                [
                    "2025-03-04",
                    -2.26
                ],
                [
                    "2025-03-05",
                    1.22
                ],
                [
                    "2025-03-07",
                    10.01
                ],
                [
                    "2025-03-10",
                    6.93
                ],
                [
                    "2025-03-11",
                    3.39
                ],
                [
                    "2025-03-12",
                    2.89
                ],
                [
                    "2025-03-13",
                    8.27
                ],
                [
                    "2025-03-14",
                    -0.53
                ],
                [
                    "2025-03-17",
                    9.01
                ],
                [
                    "2025-03-18",
                    -5.75
                ],
                [
                    "2025-03-19",
                    6.74
                ],
                [
                    "2025-03-20",
                    4.24
                ],
                [
                    "2025-03-21",
                    8.17
                ],
                [
                    "2025-03-24",
                    1.91
                ],
                [
                    "2025-03-25",
                    -4.09
                ],
                [
                    "2025-03-26",
                    -2.49
                ],
                [
                    "2025-03-27",
                    4.84
                ],
                [
                    "2025-03-28",
                    2.24
                ],
                [
                    "2025-03-31",
                    9.02
                ],
                [
                    "2025-04-01",
                    -1.37
                ],
                [
                    "2025-04-02",
                    -2.14
                ],
                [
                    "2025-04-03",
                    -4.74
                ],
                [
                    "2025-04-07",
                    5.32
                ],
                [
                    "2025-04-08",
                    4.46
                ],
                [
                    "2025-04-09",
                    -0.09
                ],
                [
                    "2025-04-11",
                    8.2
                ],
                [
                    "2025-04-14",
                    -2.92
                ],
                [
                    "2025-04-18",
                    0.65
                ],
                [
                    "2025-04-21",
                    3.39
                ],
                [
                    "2025-04-22",
                    0.23
                ],
                [
                    "2025-04-23",
                    9.21
                ],
                [
                    "2025-04-24",
                    10.55
                ],
                [
                    "2025-04-25",
                    2.91
                ],
                [
                    "2025-04-28",
                    3.82
                ],
                [
                    "2025-04-29",
                    -3.59
                ],
                [
                    "2025-04-30",
                    9.26
                ],
                [
                    "2025-05-06",
                    9.42
                ],
                [
                    "2025-05-07",
                    -6.12
                ],
                [
                    "2025-05-08",
                    6.28
                ],
                [
                    "2025-05-09",
                    4.8
                ],
                [
                    "2025-05-12",
                    16.18
                ],
                [
                    "2025-05-13",
                    7.02
                ],
                [
                    "2025-05-14",
                    9.1
                ],
                [
                    "2025-05-15",
                    -5.34
                ],
                [
                    "2025-05-16",
                    2.66
                ],
                [
                    "2025-05-19",
                    -2.99
                ],
                [
                    "2025-05-20",
                    2.83
                ],
                [
                    "2025-05-27",
                    24.03
                ],
                [
                    "2025-05-28",
                    19.85
                ],
                [
                    "2025-05-29",
                    -9.0
                ],
                [
                    "2025-05-30",
                    -8.22
                ],
                [
                    "2025-06-03",
                    -5.08
                ],
                [
                    "2025-06-04",
                    12.56
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "black"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4",
                "\u6e38\u8d44\u51c0\u5360\u6bd4",
                "\u6563\u6237\u51c0\u5360\u6bd4",
                "\u4e3b\u529b\u80cc\u79bb",
                "\u6e38\u8d44\u80cc\u79bb",
                "\u6563\u6237\u80cc\u79bb"
            ],
            "selected": {},
            "show": true,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-02",
                "2024-12-03",
                "2024-12-04",
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": -45,
                "margin": 8,
                "valueAnimation": false
            },
            "axisPointer": {
                "show": true,
                "type": "line",
                "link": [
                    {
                        "xAxisIndex": "all"
                    }
                ],
                "triggerTooltip": true,
                "triggerOn": "mousemove|click"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u51c0\u5360\u6bd4 (%)",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600459 \u4e3b\u529b\u6563\u6237\u7ebf",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ],
        [
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "xAxisIndex": "all",
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true,
        "z": 10000
    },
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "5%",
            "right": "5%",
            "height": "45%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "55%",
            "right": "5%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_84dab334bdee466590b70bbbde9d42dd.setOption(option_84dab334bdee466590b70bbbde9d42dd);
            window.addEventListener('resize', function(){
                chart_84dab334bdee466590b70bbbde9d42dd.resize();
            })
    </script>

<script>
// 等待页面完全加载
window.addEventListener('load', function() {
    setTimeout(function() {
        // 获取所有图表实例
        var charts = [];
        var chartDoms = document.querySelectorAll('[_echarts_instance_]');
        chartDoms.forEach(function(dom) {
            var chart = echarts.getInstanceByDom(dom);
            if (chart) {
                charts.push(chart);
            }
        });

        console.log('找到图表数量:', charts.length);

        if (charts.length >= 2) {
            // 确保缩放联动
            echarts.connect(charts);
            console.log('缩放联动已设置');

            var chart1 = charts[0];
            var chart2 = charts[1];

            // 简单的鼠标悬停联动
            var isUpdating = false;

            chart1.on('mousemove', function(params) {
                if (!isUpdating && params.dataIndex !== undefined) {
                    isUpdating = true;
                    chart2.dispatchAction({
                        type: 'showTip',
                        seriesIndex: 0,
                        dataIndex: params.dataIndex
                    });
                    setTimeout(function() { isUpdating = false; }, 10);
                }
            });

            chart2.on('mousemove', function(params) {
                if (!isUpdating && params.dataIndex !== undefined) {
                    isUpdating = true;
                    chart1.dispatchAction({
                        type: 'showTip',
                        seriesIndex: 0,
                        dataIndex: params.dataIndex
                    });
                    setTimeout(function() { isUpdating = false; }, 10);
                }
            });

            console.log('鼠标悬停联动已设置');
        }
    }, 2000);
});
</script>

</body>
</html>
