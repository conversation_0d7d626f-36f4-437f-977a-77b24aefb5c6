<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="ab6d35c3346641f6aacc3091be1e238e" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_ab6d35c3346641f6aacc3091be1e238e = echarts.init(
            document.getElementById('ab6d35c3346641f6aacc3091be1e238e'), 'white', {renderer: 'canvas'});
        var option_ab6d35c3346641f6aacc3091be1e238e = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K \u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    12.6,
                    12.79,
                    12.58,
                    12.88
                ],
                [
                    12.86,
                    12.91,
                    12.67,
                    13.03
                ],
                [
                    12.92,
                    13.19,
                    12.92,
                    13.82
                ],
                [
                    13.98,
                    13.23,
                    13.18,
                    14.01
                ],
                [
                    13.19,
                    13.16,
                    13.06,
                    13.28
                ],
                [
                    13.12,
                    13.32,
                    12.91,
                    13.32
                ],
                [
                    13.2,
                    13.69,
                    13.08,
                    14.08
                ],
                [
                    13.71,
                    12.67,
                    12.58,
                    13.84
                ],
                [
                    12.62,
                    12.45,
                    12.4,
                    12.75
                ],
                [
                    12.56,
                    12.61,
                    12.37,
                    12.79
                ],
                [
                    12.44,
                    12.72,
                    12.36,
                    12.77
                ],
                [
                    12.63,
                    12.73,
                    12.58,
                    12.88
                ],
                [
                    12.66,
                    12.27,
                    12.24,
                    12.73
                ],
                [
                    12.29,
                    12.31,
                    12.15,
                    12.39
                ],
                [
                    12.3,
                    12.05,
                    11.95,
                    12.31
                ],
                [
                    12.05,
                    12.19,
                    12.02,
                    12.3
                ],
                [
                    12.22,
                    12.22,
                    12.16,
                    12.57
                ],
                [
                    12.18,
                    12.26,
                    12.04,
                    12.3
                ],
                [
                    12.3,
                    11.76,
                    11.76,
                    12.36
                ],
                [
                    11.75,
                    11.22,
                    11.13,
                    11.8
                ],
                [
                    11.22,
                    10.72,
                    10.72,
                    11.3
                ],
                [
                    10.4,
                    10.69,
                    10.4,
                    10.88
                ],
                [
                    10.73,
                    11.02,
                    10.69,
                    11.02
                ],
                [
                    10.97,
                    10.72,
                    10.48,
                    10.97
                ],
                [
                    10.65,
                    10.68,
                    10.6,
                    10.8
                ],
                [
                    10.68,
                    10.37,
                    10.36,
                    10.87
                ],
                [
                    10.34,
                    10.64,
                    10.26,
                    10.68
                ],
                [
                    10.69,
                    11.0,
                    10.59,
                    11.04
                ],
                [
                    11.0,
                    10.87,
                    10.81,
                    11.03
                ],
                [
                    10.95,
                    10.87,
                    10.76,
                    11.17
                ],
                [
                    10.8,
                    10.92,
                    10.77,
                    11.0
                ],
                [
                    11.0,
                    10.98,
                    10.91,
                    11.09
                ],
                [
                    11.02,
                    11.02,
                    10.79,
                    11.11
                ],
                [
                    10.96,
                    10.89,
                    10.81,
                    10.98
                ],
                [
                    11.21,
                    11.38,
                    11.13,
                    11.71
                ],
                [
                    11.33,
                    11.52,
                    11.27,
                    11.72
                ],
                [
                    11.67,
                    11.68,
                    11.53,
                    11.88
                ],
                [
                    12.04,
                    12.22,
                    11.9,
                    12.42
                ],
                [
                    12.1,
                    12.28,
                    11.95,
                    12.28
                ],
                [
                    12.19,
                    12.72,
                    12.19,
                    12.85
                ],
                [
                    12.84,
                    13.17,
                    12.79,
                    13.26
                ],
                [
                    13.15,
                    13.03,
                    12.9,
                    13.22
                ],
                [
                    13.05,
                    13.45,
                    13.02,
                    13.5
                ],
                [
                    13.63,
                    13.26,
                    13.16,
                    13.63
                ],
                [
                    13.25,
                    13.99,
                    13.22,
                    14.19
                ],
                [
                    14.35,
                    13.95,
                    13.85,
                    14.66
                ],
                [
                    13.89,
                    13.39,
                    13.32,
                    14.23
                ],
                [
                    13.46,
                    14.06,
                    13.39,
                    14.11
                ],
                [
                    13.98,
                    13.91,
                    13.85,
                    14.34
                ],
                [
                    13.98,
                    14.24,
                    13.9,
                    14.28
                ],
                [
                    14.11,
                    13.9,
                    13.78,
                    14.24
                ],
                [
                    13.68,
                    14.1,
                    13.5,
                    14.3
                ],
                [
                    14.1,
                    14.0,
                    13.9,
                    14.25
                ],
                [
                    13.98,
                    13.72,
                    13.5,
                    14.08
                ],
                [
                    13.69,
                    13.16,
                    13.01,
                    13.75
                ],
                [
                    13.12,
                    13.26,
                    12.93,
                    13.51
                ],
                [
                    13.08,
                    13.6,
                    13.06,
                    13.76
                ],
                [
                    13.7,
                    13.82,
                    13.63,
                    14.04
                ],
                [
                    13.9,
                    15.2,
                    13.84,
                    15.2
                ],
                [
                    15.5,
                    15.05,
                    14.83,
                    15.53
                ],
                [
                    15.05,
                    14.8,
                    14.59,
                    15.09
                ],
                [
                    14.5,
                    14.63,
                    14.36,
                    14.9
                ],
                [
                    14.75,
                    14.42,
                    14.39,
                    14.77
                ],
                [
                    14.49,
                    14.21,
                    14.07,
                    14.52
                ],
                [
                    14.15,
                    14.43,
                    13.91,
                    14.48
                ],
                [
                    14.62,
                    15.0,
                    14.4,
                    15.09
                ],
                [
                    15.0,
                    15.0,
                    14.82,
                    15.33
                ],
                [
                    14.89,
                    15.04,
                    14.78,
                    15.2
                ],
                [
                    14.97,
                    14.67,
                    14.65,
                    15.01
                ],
                [
                    14.55,
                    14.34,
                    14.21,
                    14.62
                ],
                [
                    14.32,
                    13.53,
                    13.31,
                    14.32
                ],
                [
                    13.8,
                    14.02,
                    13.75,
                    14.27
                ],
                [
                    13.97,
                    14.11,
                    13.85,
                    14.25
                ],
                [
                    14.02,
                    14.11,
                    13.77,
                    14.24
                ],
                [
                    14.11,
                    14.03,
                    13.97,
                    14.23
                ],
                [
                    13.91,
                    14.49,
                    13.8,
                    14.58
                ],
                [
                    14.44,
                    14.49,
                    14.32,
                    14.77
                ],
                [
                    14.45,
                    14.62,
                    14.4,
                    15.02
                ],
                [
                    14.47,
                    14.7,
                    14.4,
                    14.99
                ],
                [
                    13.7,
                    13.26,
                    13.23,
                    14.49
                ],
                [
                    13.59,
                    13.86,
                    13.56,
                    14.1
                ],
                [
                    13.72,
                    14.51,
                    13.23,
                    14.6
                ],
                [
                    14.65,
                    14.69,
                    14.65,
                    15.05
                ],
                [
                    14.55,
                    14.6,
                    14.54,
                    14.84
                ],
                [
                    14.77,
                    14.65,
                    14.57,
                    14.96
                ],
                [
                    14.59,
                    14.25,
                    13.99,
                    14.64
                ],
                [
                    14.19,
                    14.37,
                    14.12,
                    14.8
                ],
                [
                    14.18,
                    15.81,
                    14.12,
                    15.81
                ],
                [
                    16.03,
                    15.43,
                    15.4,
                    16.2
                ],
                [
                    15.51,
                    15.78,
                    15.22,
                    15.93
                ],
                [
                    15.75,
                    15.55,
                    15.46,
                    15.89
                ],
                [
                    15.65,
                    15.15,
                    15.1,
                    15.78
                ],
                [
                    15.1,
                    14.98,
                    14.86,
                    15.26
                ],
                [
                    14.96,
                    14.8,
                    14.77,
                    15.35
                ],
                [
                    14.75,
                    14.37,
                    14.33,
                    15.05
                ],
                [
                    14.55,
                    15.05,
                    14.46,
                    15.25
                ],
                [
                    15.01,
                    14.75,
                    14.74,
                    15.18
                ],
                [
                    14.78,
                    15.16,
                    14.78,
                    15.22
                ],
                [
                    15.31,
                    15.11,
                    14.97,
                    15.49
                ],
                [
                    15.05,
                    15.07,
                    15.01,
                    15.25
                ],
                [
                    15.0,
                    14.63,
                    14.6,
                    15.01
                ],
                [
                    14.75,
                    14.8,
                    14.66,
                    14.88
                ],
                [
                    14.95,
                    14.66,
                    14.55,
                    14.97
                ],
                [
                    14.6,
                    14.73,
                    14.53,
                    14.85
                ],
                [
                    14.65,
                    14.06,
                    14.01,
                    14.66
                ],
                [
                    14.02,
                    13.97,
                    13.91,
                    14.12
                ],
                [
                    13.98,
                    14.21,
                    13.83,
                    14.25
                ],
                [
                    14.21,
                    14.14,
                    14.09,
                    14.24
                ],
                [
                    14.15,
                    14.06,
                    14.01,
                    14.18
                ],
                [
                    14.0,
                    13.9,
                    13.88,
                    14.16
                ],
                [
                    13.89,
                    13.88,
                    13.86,
                    14.23
                ],
                [
                    13.91,
                    13.91,
                    13.78,
                    14.03
                ],
                [
                    13.93,
                    13.82,
                    13.75,
                    13.95
                ],
                [
                    13.86,
                    13.66,
                    13.66,
                    13.93
                ],
                [
                    13.57,
                    14.01,
                    13.49,
                    14.05
                ],
                [
                    14.0,
                    13.67,
                    13.62,
                    14.0
                ],
                [
                    13.8,
                    14.17,
                    13.75,
                    14.37
                ],
                [
                    14.2,
                    14.3,
                    14.13,
                    14.32
                ],
                [
                    14.3,
                    14.45,
                    14.22,
                    14.49
                ],
                [
                    14.45,
                    14.42,
                    14.32,
                    14.55
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "bar",
            "name": "\u6210\u4ea4\u91cf",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "legendHoverLink": true,
            "data": [
                285129.64,
                375166.26,
                737571.42,
                779527.76,
                433789.39,
                499593.33,
                1409442.14,
                1125303.41,
                450704.18,
                360709.25,
                329475.17,
                358351.94,
                348549.01,
                228312.59,
                285413.72,
                217745.01,
                318850.67,
                206123.94,
                340246.22,
                374822.08,
                377072.4,
                302982.02,
                305781.5,
                322667.2,
                220084.01,
                251355.27,
                274223.24,
                356486.34,
                275163.7,
                287716.94,
                224364.46,
                208085.24,
                223093.81,
                215115.73,
                641587.91,
                612151.78,
                454319.79,
                723822.55,
                540136.15,
                787200.18,
                747736.8,
                653735.87,
                604817.02,
                525102.44,
                974164.26,
                1053247.22,
                801097.54,
                624509.54,
                463598.39,
                522600.57,
                486587.11,
                536012.44,
                338994.62,
                390560.5,
                311004.08,
                290042.44,
                313068.34,
                341523.45,
                744566.73,
                1054382.39,
                445624.35,
                347106.06,
                317001.06,
                278762.62,
                345976.67,
                608193.2,
                393198.87,
                280014.82,
                247447.5,
                240613.37,
                387578.11,
                414742.38,
                204003.5,
                183007.88,
                148522.4,
                358309.27,
                348847.48,
                302310.35,
                285748.49,
                486267.4,
                440146.52,
                548953.38,
                448220.26,
                293733.32,
                362978.36,
                338939.86,
                539110.35,
                683360.41,
                872665.6,
                705937.96,
                596526.87,
                546817.04,
                405221.29,
                512636.13,
                423754.12,
                521660.8,
                423596.01,
                410268.34,
                426891.06,
                279247.8,
                274872.17,
                200853.3,
                208030.99,
                212419.0,
                369702.94,
                193242.49,
                197589.75,
                177407.79,
                143990.05,
                139503.78,
                220810.0,
                136201.0,
                108147.8,
                125733.99,
                273542.72,
                223790.34,
                339733.17,
                184399.82,
                211056.19,
                156371.28
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "#7f7f7f"
            }
        },
        {
            "type": "line",
            "name": "\u4e3b\u529b\u51c0\u5360\u6bd4",
            "connectNulls": false,
            "xAxisIndex": 2,
            "yAxisIndex": 2,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2024-12-05",
                    -2.16
                ],
                [
                    "2024-12-06",
                    3.79
                ],
                [
                    "2024-12-09",
                    7.09
                ],
                [
                    "2024-12-10",
                    -2.21
                ],
                [
                    "2024-12-11",
                    -11.63
                ],
                [
                    "2024-12-12",
                    -2.65
                ],
                [
                    "2024-12-13",
                    4.74
                ],
                [
                    "2024-12-16",
                    -12.64
                ],
                [
                    "2024-12-17",
                    -7.05
                ],
                [
                    "2024-12-18",
                    1.84
                ],
                [
                    "2024-12-19",
                    -5.08
                ],
                [
                    "2024-12-20",
                    -0.06
                ],
                [
                    "2024-12-23",
                    -9.92
                ],
                [
                    "2024-12-24",
                    -8.16
                ],
                [
                    "2024-12-25",
                    -29.76
                ],
                [
                    "2024-12-26",
                    -0.34
                ],
                [
                    "2024-12-27",
                    -0.43
                ],
                [
                    "2024-12-30",
                    -0.36
                ],
                [
                    "2024-12-31",
                    -1.86
                ],
                [
                    "2025-01-02",
                    2.2
                ],
                [
                    "2025-01-03",
                    -3.7
                ],
                [
                    "2025-01-06",
                    -5.83
                ],
                [
                    "2025-01-07",
                    -0.93
                ],
                [
                    "2025-01-08",
                    -6.54
                ],
                [
                    "2025-01-09",
                    0.5
                ],
                [
                    "2025-01-10",
                    -2.54
                ],
                [
                    "2025-01-13",
                    -6.27
                ],
                [
                    "2025-01-14",
                    0.53
                ],
                [
                    "2025-01-15",
                    -3.04
                ],
                [
                    "2025-01-16",
                    1.63
                ],
                [
                    "2025-01-17",
                    2.79
                ],
                [
                    "2025-01-20",
                    -8.15
                ],
                [
                    "2025-01-21",
                    5.44
                ],
                [
                    "2025-01-22",
                    -8.53
                ],
                [
                    "2025-01-23",
                    4.31
                ],
                [
                    "2025-01-24",
                    1.2
                ],
                [
                    "2025-01-27",
                    7.46
                ],
                [
                    "2025-02-05",
                    14.07
                ],
                [
                    "2025-02-06",
                    0.96
                ],
                [
                    "2025-02-07",
                    1.62
                ],
                [
                    "2025-02-10",
                    6.0
                ],
                [
                    "2025-02-11",
                    -6.7
                ],
                [
                    "2025-02-12",
                    0.49
                ],
                [
                    "2025-02-13",
                    -4.22
                ],
                [
                    "2025-02-14",
                    2.12
                ],
                [
                    "2025-02-17",
                    -12.33
                ],
                [
                    "2025-02-18",
                    -11.94
                ],
                [
                    "2025-02-19",
                    0.31
                ],
                [
                    "2025-02-20",
                    -5.63
                ],
                [
                    "2025-02-21",
                    -2.6
                ],
                [
                    "2025-02-24",
                    -9.8
                ],
                [
                    "2025-02-25",
                    -9.96
                ],
                [
                    "2025-02-26",
                    -13.69
                ],
                [
                    "2025-02-27",
                    -14.68
                ],
                [
                    "2025-02-28",
                    -8.35
                ],
                [
                    "2025-03-03",
                    -1.89
                ],
                [
                    "2025-03-04",
                    -2.86
                ],
                [
                    "2025-03-05",
                    -3.37
                ],
                [
                    "2025-03-06",
                    18.47
                ],
                [
                    "2025-03-07",
                    -5.48
                ],
                [
                    "2025-03-10",
                    -1.4
                ],
                [
                    "2025-03-11",
                    -3.59
                ],
                [
                    "2025-03-12",
                    -12.59
                ],
                [
                    "2025-03-13",
                    -9.91
                ],
                [
                    "2025-03-14",
                    -4.0
                ],
                [
                    "2025-03-17",
                    4.8
                ],
                [
                    "2025-03-18",
                    -2.64
                ],
                [
                    "2025-03-19",
                    8.51
                ],
                [
                    "2025-03-20",
                    -7.08
                ],
                [
                    "2025-03-21",
                    -9.18
                ],
                [
                    "2025-03-24",
                    -9.52
                ],
                [
                    "2025-03-25",
                    4.2
                ],
                [
                    "2025-03-26",
                    0.78
                ],
                [
                    "2025-03-27",
                    3.17
                ],
                [
                    "2025-03-28",
                    -11.5
                ],
                [
                    "2025-03-31",
                    11.13
                ],
                [
                    "2025-04-01",
                    4.45
                ],
                [
                    "2025-04-02",
                    6.52
                ],
                [
                    "2025-04-03",
                    6.66
                ],
                [
                    "2025-04-07",
                    -10.88
                ],
                [
                    "2025-04-08",
                    -1.07
                ],
                [
                    "2025-04-09",
                    8.35
                ],
                [
                    "2025-04-10",
                    1.14
                ],
                [
                    "2025-04-11",
                    3.75
                ],
                [
                    "2025-04-14",
                    3.76
                ],
                [
                    "2025-04-15",
                    -9.35
                ],
                [
                    "2025-04-16",
                    2.0
                ],
                [
                    "2025-04-17",
                    27.91
                ],
                [
                    "2025-04-18",
                    -7.41
                ],
                [
                    "2025-04-21",
                    0.08
                ],
                [
                    "2025-04-22",
                    -1.68
                ],
                [
                    "2025-04-23",
                    -9.59
                ],
                [
                    "2025-04-24",
                    -4.81
                ],
                [
                    "2025-04-25",
                    -7.1
                ],
                [
                    "2025-04-28",
                    -9.57
                ],
                [
                    "2025-04-29",
                    9.47
                ],
                [
                    "2025-04-30",
                    -6.55
                ],
                [
                    "2025-05-06",
                    -0.26
                ],
                [
                    "2025-05-07",
                    1.91
                ],
                [
                    "2025-05-08",
                    -4.93
                ],
                [
                    "2025-05-09",
                    -18.6
                ],
                [
                    "2025-05-12",
                    -0.24
                ],
                [
                    "2025-05-13",
                    -11.35
                ],
                [
                    "2025-05-14",
                    1.63
                ],
                [
                    "2025-05-15",
                    -14.89
                ],
                [
                    "2025-05-16",
                    -7.17
                ],
                [
                    "2025-05-19",
                    5.61
                ],
                [
                    "2025-05-20",
                    -3.33
                ],
                [
                    "2025-05-21",
                    -8.33
                ],
                [
                    "2025-05-22",
                    -15.82
                ],
                [
                    "2025-05-23",
                    -7.93
                ],
                [
                    "2025-05-26",
                    -1.31
                ],
                [
                    "2025-05-27",
                    -4.59
                ],
                [
                    "2025-05-28",
                    -11.91
                ],
                [
                    "2025-05-29",
                    4.68
                ],
                [
                    "2025-05-30",
                    -1.73
                ],
                [
                    "2025-06-03",
                    -0.35
                ],
                [
                    "2025-06-04",
                    4.67
                ],
                [
                    "2025-06-05",
                    3.13
                ],
                [
                    "2025-06-06",
                    -9.88
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 2,
                "opacity": 1,
                "curveness": 0,
                "type": "solid",
                "color": "#5470C6"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "scatter",
            "name": "\u4e3b\u529b\u5165\u573a\u4fe1\u53f7",
            "xAxisIndex": 3,
            "yAxisIndex": 3,
            "symbol": "triangle",
            "symbolSize": 12,
            "data": [
                [
                    "2024-12-05",
                    null
                ],
                [
                    "2024-12-06",
                    12.67
                ],
                [
                    "2024-12-09",
                    null
                ],
                [
                    "2024-12-10",
                    null
                ],
                [
                    "2024-12-11",
                    null
                ],
                [
                    "2024-12-12",
                    null
                ],
                [
                    "2024-12-13",
                    null
                ],
                [
                    "2024-12-16",
                    null
                ],
                [
                    "2024-12-17",
                    null
                ],
                [
                    "2024-12-18",
                    null
                ],
                [
                    "2024-12-19",
                    null
                ],
                [
                    "2024-12-20",
                    null
                ],
                [
                    "2024-12-23",
                    null
                ],
                [
                    "2024-12-24",
                    null
                ],
                [
                    "2024-12-25",
                    null
                ],
                [
                    "2024-12-26",
                    null
                ],
                [
                    "2024-12-27",
                    null
                ],
                [
                    "2024-12-30",
                    null
                ],
                [
                    "2024-12-31",
                    null
                ],
                [
                    "2025-01-02",
                    null
                ],
                [
                    "2025-01-03",
                    null
                ],
                [
                    "2025-01-06",
                    null
                ],
                [
                    "2025-01-07",
                    null
                ],
                [
                    "2025-01-08",
                    null
                ],
                [
                    "2025-01-09",
                    null
                ],
                [
                    "2025-01-10",
                    null
                ],
                [
                    "2025-01-13",
                    null
                ],
                [
                    "2025-01-14",
                    null
                ],
                [
                    "2025-01-15",
                    null
                ],
                [
                    "2025-01-16",
                    null
                ],
                [
                    "2025-01-17",
                    null
                ],
                [
                    "2025-01-20",
                    null
                ],
                [
                    "2025-01-21",
                    null
                ],
                [
                    "2025-01-22",
                    null
                ],
                [
                    "2025-01-23",
                    null
                ],
                [
                    "2025-01-24",
                    null
                ],
                [
                    "2025-01-27",
                    11.53
                ],
                [
                    "2025-02-05",
                    null
                ],
                [
                    "2025-02-06",
                    null
                ],
                [
                    "2025-02-07",
                    null
                ],
                [
                    "2025-02-10",
                    null
                ],
                [
                    "2025-02-11",
                    null
                ],
                [
                    "2025-02-12",
                    null
                ],
                [
                    "2025-02-13",
                    null
                ],
                [
                    "2025-02-14",
                    null
                ],
                [
                    "2025-02-17",
                    null
                ],
                [
                    "2025-02-18",
                    null
                ],
                [
                    "2025-02-19",
                    null
                ],
                [
                    "2025-02-20",
                    null
                ],
                [
                    "2025-02-21",
                    null
                ],
                [
                    "2025-02-24",
                    null
                ],
                [
                    "2025-02-25",
                    null
                ],
                [
                    "2025-02-26",
                    null
                ],
                [
                    "2025-02-27",
                    null
                ],
                [
                    "2025-02-28",
                    null
                ],
                [
                    "2025-03-03",
                    null
                ],
                [
                    "2025-03-04",
                    null
                ],
                [
                    "2025-03-05",
                    null
                ],
                [
                    "2025-03-06",
                    null
                ],
                [
                    "2025-03-07",
                    null
                ],
                [
                    "2025-03-10",
                    null
                ],
                [
                    "2025-03-11",
                    null
                ],
                [
                    "2025-03-12",
                    null
                ],
                [
                    "2025-03-13",
                    null
                ],
                [
                    "2025-03-14",
                    null
                ],
                [
                    "2025-03-17",
                    null
                ],
                [
                    "2025-03-18",
                    null
                ],
                [
                    "2025-03-19",
                    null
                ],
                [
                    "2025-03-20",
                    null
                ],
                [
                    "2025-03-21",
                    null
                ],
                [
                    "2025-03-24",
                    null
                ],
                [
                    "2025-03-25",
                    null
                ],
                [
                    "2025-03-26",
                    null
                ],
                [
                    "2025-03-27",
                    null
                ],
                [
                    "2025-03-28",
                    null
                ],
                [
                    "2025-03-31",
                    13.8
                ],
                [
                    "2025-04-01",
                    14.32
                ],
                [
                    "2025-04-02",
                    14.4
                ],
                [
                    "2025-04-03",
                    null
                ],
                [
                    "2025-04-07",
                    null
                ],
                [
                    "2025-04-08",
                    null
                ],
                [
                    "2025-04-09",
                    null
                ],
                [
                    "2025-04-10",
                    null
                ],
                [
                    "2025-04-11",
                    14.54
                ],
                [
                    "2025-04-14",
                    null
                ],
                [
                    "2025-04-15",
                    null
                ],
                [
                    "2025-04-16",
                    null
                ],
                [
                    "2025-04-17",
                    null
                ],
                [
                    "2025-04-18",
                    null
                ],
                [
                    "2025-04-21",
                    null
                ],
                [
                    "2025-04-22",
                    null
                ],
                [
                    "2025-04-23",
                    null
                ],
                [
                    "2025-04-24",
                    null
                ],
                [
                    "2025-04-25",
                    null
                ],
                [
                    "2025-04-28",
                    null
                ],
                [
                    "2025-04-29",
                    null
                ],
                [
                    "2025-04-30",
                    null
                ],
                [
                    "2025-05-06",
                    null
                ],
                [
                    "2025-05-07",
                    null
                ],
                [
                    "2025-05-08",
                    null
                ],
                [
                    "2025-05-09",
                    null
                ],
                [
                    "2025-05-12",
                    null
                ],
                [
                    "2025-05-13",
                    null
                ],
                [
                    "2025-05-14",
                    null
                ],
                [
                    "2025-05-15",
                    null
                ],
                [
                    "2025-05-16",
                    null
                ],
                [
                    "2025-05-19",
                    null
                ],
                [
                    "2025-05-20",
                    null
                ],
                [
                    "2025-05-21",
                    null
                ],
                [
                    "2025-05-22",
                    null
                ],
                [
                    "2025-05-23",
                    null
                ],
                [
                    "2025-05-26",
                    null
                ],
                [
                    "2025-05-27",
                    null
                ],
                [
                    "2025-05-28",
                    null
                ],
                [
                    "2025-05-29",
                    null
                ],
                [
                    "2025-05-30",
                    null
                ],
                [
                    "2025-06-03",
                    null
                ],
                [
                    "2025-06-04",
                    14.13
                ],
                [
                    "2025-06-05",
                    null
                ],
                [
                    "2025-06-06",
                    null
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "purple"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K \u7ebf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u6210\u4ea4\u91cf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u51c0\u5360\u6bd4"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4e3b\u529b\u5165\u573a\u4fe1\u53f7"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "splitArea": {
                "show": true,
                "areaStyle": {
                    "opacity": 1
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 2,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "position": "right",
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "002410 \u4e3b\u529b\u5165\u573a\u4fe1\u53f7 K \u7ebf\u56fe",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 50,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 50,
            "end": 100,
            "orient": "horizontal",
            "xAxisIndex": [
                0,
                1
            ],
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 50,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 50,
                "end": 100,
                "orient": "horizontal",
                "xAxisIndex": [
                    0,
                    1
                ],
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "5%",
            "right": "8%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "50%",
            "right": "8%",
            "height": "15%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "70%",
            "right": "8%",
            "height": "15%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "5%",
            "right": "8%",
            "height": "40%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_ab6d35c3346641f6aacc3091be1e238e.setOption(option_ab6d35c3346641f6aacc3091be1e238e);
            window.addEventListener('resize', function(){
                chart_ab6d35c3346641f6aacc3091be1e238e.resize();
            })
    </script>
</body>
</html>
