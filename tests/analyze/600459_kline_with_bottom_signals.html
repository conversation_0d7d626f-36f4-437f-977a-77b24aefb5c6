<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="8b28a334762145778f270505fbb7c6ad" class="chart-container" style="width:100%; height:400px; "></div>
    <script>
        var chart_8b28a334762145778f270505fbb7c6ad = echarts.init(
            document.getElementById('8b28a334762145778f270505fbb7c6ad'), 'white', {renderer: 'canvas'});
        var option_8b28a334762145778f270505fbb7c6ad = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K Line",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "data": [
                [
                    14.0,
                    14.03,
                    13.84,
                    14.06
                ],
                [
                    14.03,
                    14.16,
                    13.9,
                    14.25
                ],
                [
                    14.18,
                    14.15,
                    14.06,
                    14.34
                ],
                [
                    14.4,
                    14.19,
                    14.17,
                    14.49
                ],
                [
                    14.18,
                    14.36,
                    14.12,
                    14.4
                ],
                [
                    14.32,
                    14.47,
                    14.27,
                    14.5
                ],
                [
                    14.41,
                    14.16,
                    14.15,
                    14.43
                ],
                [
                    14.2,
                    14.17,
                    14.12,
                    14.23
                ],
                [
                    14.16,
                    14.1,
                    14.02,
                    14.24
                ],
                [
                    14.12,
                    14.01,
                    13.98,
                    14.18
                ],
                [
                    13.95,
                    14.03,
                    13.79,
                    14.07
                ],
                [
                    14.02,
                    14.0,
                    13.95,
                    14.07
                ],
                [
                    14.0,
                    13.84,
                    13.82,
                    14.05
                ],
                [
                    13.84,
                    14.04,
                    13.84,
                    14.05
                ],
                [
                    14.08,
                    14.04,
                    13.93,
                    14.1
                ],
                [
                    14.04,
                    13.95,
                    13.93,
                    14.1
                ],
                [
                    13.94,
                    14.11,
                    13.92,
                    14.18
                ],
                [
                    14.06,
                    14.07,
                    14.01,
                    14.11
                ],
                [
                    14.05,
                    13.73,
                    13.71,
                    14.1
                ],
                [
                    13.78,
                    13.45,
                    13.38,
                    13.89
                ],
                [
                    13.5,
                    13.42,
                    13.37,
                    13.85
                ],
                [
                    13.45,
                    13.56,
                    13.31,
                    13.82
                ],
                [
                    13.56,
                    13.61,
                    13.44,
                    13.75
                ],
                [
                    13.56,
                    13.42,
                    13.18,
                    13.58
                ],
                [
                    13.31,
                    13.59,
                    13.31,
                    13.74
                ],
                [
                    13.69,
                    13.46,
                    13.46,
                    13.76
                ],
                [
                    13.52,
                    13.7,
                    13.42,
                    13.82
                ],
                [
                    13.64,
                    13.95,
                    13.59,
                    13.96
                ],
                [
                    13.96,
                    13.8,
                    13.75,
                    13.99
                ],
                [
                    13.85,
                    14.01,
                    13.85,
                    14.06
                ],
                [
                    14.19,
                    13.76,
                    13.74,
                    14.3
                ],
                [
                    13.78,
                    13.63,
                    13.61,
                    13.81
                ],
                [
                    13.66,
                    13.64,
                    13.5,
                    13.7
                ],
                [
                    13.6,
                    13.49,
                    13.45,
                    13.67
                ],
                [
                    13.55,
                    13.42,
                    13.42,
                    13.71
                ],
                [
                    13.42,
                    13.63,
                    13.38,
                    13.63
                ],
                [
                    13.68,
                    13.72,
                    13.57,
                    13.83
                ],
                [
                    13.86,
                    13.85,
                    13.73,
                    13.9
                ],
                [
                    13.82,
                    14.08,
                    13.81,
                    14.1
                ],
                [
                    14.02,
                    14.04,
                    13.95,
                    14.11
                ],
                [
                    13.99,
                    13.96,
                    13.93,
                    14.08
                ],
                [
                    14.01,
                    14.03,
                    13.98,
                    14.12
                ],
                [
                    13.99,
                    13.94,
                    13.83,
                    14.03
                ],
                [
                    13.92,
                    13.84,
                    13.84,
                    13.98
                ],
                [
                    13.84,
                    13.87,
                    13.75,
                    13.92
                ],
                [
                    13.87,
                    13.66,
                    13.62,
                    13.87
                ],
                [
                    13.66,
                    13.56,
                    13.51,
                    13.73
                ],
                [
                    13.57,
                    13.61,
                    13.56,
                    13.66
                ],
                [
                    13.62,
                    13.69,
                    13.59,
                    13.73
                ],
                [
                    13.7,
                    13.73,
                    13.66,
                    13.77
                ],
                [
                    13.72,
                    13.73,
                    13.56,
                    13.8
                ],
                [
                    13.69,
                    13.55,
                    13.52,
                    13.71
                ],
                [
                    13.55,
                    13.63,
                    13.53,
                    13.68
                ],
                [
                    13.64,
                    13.5,
                    13.39,
                    13.7
                ],
                [
                    13.47,
                    13.4,
                    13.36,
                    13.57
                ],
                [
                    13.45,
                    13.54,
                    13.41,
                    13.7
                ],
                [
                    13.51,
                    13.66,
                    13.45,
                    13.69
                ],
                [
                    13.6,
                    13.64,
                    13.47,
                    13.67
                ],
                [
                    13.67,
                    13.73,
                    13.63,
                    13.76
                ],
                [
                    13.66,
                    13.98,
                    13.66,
                    14.07
                ],
                [
                    14.06,
                    14.16,
                    14.05,
                    14.28
                ],
                [
                    14.16,
                    14.59,
                    14.07,
                    14.63
                ],
                [
                    14.53,
                    14.36,
                    14.33,
                    14.62
                ],
                [
                    14.39,
                    14.5,
                    14.26,
                    14.52
                ],
                [
                    14.75,
                    14.5,
                    14.44,
                    14.88
                ],
                [
                    14.5,
                    14.32,
                    14.28,
                    14.51
                ],
                [
                    14.32,
                    14.46,
                    14.24,
                    14.63
                ],
                [
                    14.48,
                    14.42,
                    14.35,
                    14.53
                ],
                [
                    14.42,
                    14.34,
                    14.29,
                    14.47
                ],
                [
                    14.3,
                    14.16,
                    14.09,
                    14.42
                ],
                [
                    14.17,
                    14.29,
                    14.06,
                    14.38
                ],
                [
                    14.23,
                    14.44,
                    14.2,
                    14.51
                ],
                [
                    14.48,
                    14.35,
                    14.29,
                    14.61
                ],
                [
                    14.28,
                    14.17,
                    14.05,
                    14.3
                ],
                [
                    14.22,
                    14.07,
                    13.95,
                    14.27
                ],
                [
                    14.02,
                    13.95,
                    13.79,
                    14.1
                ],
                [
                    13.95,
                    14.03,
                    13.92,
                    14.12
                ],
                [
                    14.04,
                    13.88,
                    13.84,
                    14.06
                ],
                [
                    13.85,
                    13.88,
                    13.73,
                    13.92
                ],
                [
                    13.14,
                    12.49,
                    12.49,
                    13.21
                ],
                [
                    12.54,
                    12.45,
                    12.27,
                    12.68
                ],
                [
                    12.31,
                    12.55,
                    11.91,
                    12.6
                ],
                [
                    12.72,
                    12.78,
                    12.69,
                    12.91
                ],
                [
                    12.78,
                    12.92,
                    12.71,
                    13.03
                ],
                [
                    12.98,
                    13.09,
                    12.98,
                    13.17
                ],
                [
                    13.07,
                    13.08,
                    12.91,
                    13.1
                ],
                [
                    13.08,
                    12.97,
                    12.8,
                    13.13
                ],
                [
                    12.97,
                    12.95,
                    12.92,
                    13.18
                ],
                [
                    12.9,
                    12.91,
                    12.79,
                    12.98
                ],
                [
                    12.92,
                    13.22,
                    12.87,
                    13.27
                ],
                [
                    13.25,
                    13.42,
                    13.14,
                    13.47
                ],
                [
                    13.28,
                    13.14,
                    13.14,
                    13.41
                ],
                [
                    13.15,
                    13.0,
                    12.97,
                    13.23
                ],
                [
                    13.28,
                    13.2,
                    13.18,
                    13.45
                ],
                [
                    13.25,
                    13.14,
                    13.08,
                    13.29
                ],
                [
                    13.17,
                    13.11,
                    13.06,
                    13.25
                ],
                [
                    13.17,
                    13.18,
                    13.16,
                    13.39
                ],
                [
                    13.22,
                    13.45,
                    13.22,
                    13.54
                ],
                [
                    13.49,
                    13.49,
                    13.38,
                    13.56
                ],
                [
                    13.44,
                    13.42,
                    13.38,
                    13.55
                ],
                [
                    13.42,
                    13.39,
                    13.27,
                    13.45
                ],
                [
                    13.4,
                    13.54,
                    13.37,
                    13.62
                ],
                [
                    13.62,
                    13.44,
                    13.4,
                    13.62
                ],
                [
                    13.45,
                    13.54,
                    13.36,
                    13.58
                ],
                [
                    13.54,
                    13.39,
                    13.38,
                    13.56
                ],
                [
                    13.4,
                    13.42,
                    13.39,
                    13.59
                ],
                [
                    13.42,
                    13.41,
                    13.29,
                    13.49
                ],
                [
                    13.4,
                    13.5,
                    13.38,
                    13.55
                ],
                [
                    13.73,
                    13.94,
                    13.73,
                    14.09
                ],
                [
                    13.97,
                    14.08,
                    13.88,
                    14.64
                ],
                [
                    14.29,
                    14.42,
                    14.25,
                    14.68
                ],
                [
                    14.6,
                    14.49,
                    14.35,
                    14.65
                ],
                [
                    14.44,
                    14.22,
                    14.13,
                    14.45
                ],
                [
                    14.17,
                    14.13,
                    14.04,
                    14.25
                ],
                [
                    14.09,
                    14.09,
                    14.02,
                    14.19
                ],
                [
                    14.08,
                    14.08,
                    14.0,
                    14.21
                ],
                [
                    14.11,
                    14.33,
                    14.08,
                    14.51
                ],
                [
                    14.35,
                    14.5,
                    14.33,
                    14.59
                ],
                [
                    14.6,
                    14.51,
                    14.48,
                    14.89
                ],
                [
                    14.98,
                    15.47,
                    14.91,
                    15.57
                ]
            ],
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "scatter",
            "name": "Bottom Signal",
            "symbol": "circle",
            "symbolSize": 10,
            "data": [
                [
                    "2025-02-25",
                    13.25
                ],
                [
                    "2025-02-27",
                    13.12
                ],
                [
                    "2025-02-28",
                    13.09
                ],
                [
                    "2025-04-03",
                    13.46
                ],
                [
                    "2025-04-07",
                    12.24
                ],
                [
                    "2025-04-08",
                    12.02
                ],
                [
                    "2025-04-10",
                    12.44
                ]
            ],
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color": "green"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K Line",
                "Bottom Signal"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2024-12-05",
                "2024-12-06",
                "2024-12-09",
                "2024-12-10",
                "2024-12-11",
                "2024-12-12",
                "2024-12-13",
                "2024-12-16",
                "2024-12-17",
                "2024-12-18",
                "2024-12-19",
                "2024-12-20",
                "2024-12-23",
                "2024-12-24",
                "2024-12-25",
                "2024-12-26",
                "2024-12-27",
                "2024-12-30",
                "2024-12-31",
                "2025-01-02",
                "2025-01-03",
                "2025-01-06",
                "2025-01-07",
                "2025-01-08",
                "2025-01-09",
                "2025-01-10",
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "600459 K \u7ebf\u56fe\u4e0e\u5e95\u90e8\u4fe1\u53f7",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_8b28a334762145778f270505fbb7c6ad.setOption(option_8b28a334762145778f270505fbb7c6ad);
            window.addEventListener('resize', function(){
                chart_8b28a334762145778f270505fbb7c6ad.resize();
            })
    </script>
</body>
</html>
